/* select toutes les séances futures d'une liste d'evenements, avec grille de tarifs, filtrés par operateur et filieres */




create table #myResult (eventId int, 
eventName varchar(50),
sessionId int, sessionStartDate datetime, sessionEndDate datetime, lieuId int, lieuName varchar(50), seance_verrouiller varchar(1), afficherLaDate int   )
declare @manifId int



insert into #myResult SELECT distinct s.manifestation_id as eventId, 
	m.manifestation_nom as eventName, s.seance_id as sessionId, seance_date_deb as sessionStartDate, seance_date_fin as sessionEndDate,
	l.lieu_id as lieuId, lieu_nom as lieuName, seance_verrouiller,
	case s.NEPASAFFICHERDATE 
		 when 'O' then 0
		 when 'N' then 1
		 when '' then 1
		 when null then 1
	 END
	 as afficherLaDate
	FROM seance s
	INNER JOIN lieu l on l.lieu_id = s.lieu_id

	inner join valeur_tarif_stock[eventID] vts on s.seance_Id = vts.seance_id
	INNER JOIN type_tarif tt ON tt.type_tarif_id = vts.type_tarif_id
	inner join filieres_droits fd ON fd.type_tarif_id = tt.type_tarif_id 

	INNER JOIN operateur_droit od ON od.droit_table_id = tt.type_tarif_id

	inner join manifestation m on m.manifestation_id = s.manifestation_id

	WHERE s.manifestation_id =[eventID]
	AND seance_date_fin >getdate()
	and s.seance_date_deb > dateadd(minute,5,getdate())
	
	and s.seance_date_deb < dateadd(year,4, getdate() )
	AND seance_cloturer<>'O' AND seance_masquer<>'O' AND seance_verrouiller<>'O'
	AND od.operateur_id in ({listoperateursId}) AND OPTIONS <>'O'

	AND filiere_id in ({listfilieresId}) AND valeur='O'
	AND od.droit_valeur =1 and droit_table ='TYPE_TARIF'

	AND vts_v =(	SELECT MAX(vts_v) FROM valeur_tarif_stock[eventID] vts2
					WHERE vts2.tarif_logique_id=vts.tarif_logique_id
					and vts2.seance_id=vts.seance_id
					and vts2.categ_id= vts.categ_id
					and vts2.type_tarif_id= vts.type_tarif_id
		) and vts.vts_grille1>=0


	ORDER BY s.manifestation_id, s.seance_date_fin, s.seance_date_deb;


select * from #myResult

drop table #myResult

