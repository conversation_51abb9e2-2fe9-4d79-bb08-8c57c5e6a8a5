﻿/*
declare @checkEmailUnicity bit
set @checkEmailUnicity = 0

-- correspond à EMAIL et TYPE_EMAIL dans le config.ini 
declare @pPostalTelEmail int
set @pPostalTelEmail= 6

declare @plibelleEmail int
set @plibelleEmail= 5120005

-- correspond à TELEPHONE et TYPE_TELEPHONE dans le config.ini 
declare @pPostalTelPhone int
set @pPostalTelPhone= 1

declare @plibellePhone int
set @plibellePhone= 5120001

-- correspond à FAX et TYPE_FAX dans le config.ini 
declare @pPostalTelFax int
set @pPostalTelFax = 2

declare @plibelleFax int
set @plibelleFax= 5120004 

-- correspond à PORTABLE et TYPE_PORTABLE dans le config.ini 
declare @pPostalTelMobilePhone int
set @pPostalTelMobilePhone= 3

declare @plibelleMobilePhone int
set @plibelleMobilePhone= 5120003


declare @pemail varchar(50)
set @pemail ='<EMAIL>'

declare @pFax varchar(50)
set @pFax =''

declare @pPhoneNumber varchar(50)
set @pPhoneNumber =''

declare @pMobilePhoneNumber varchar(50)
set @pMobilePhoneNumber =''


declare @pIdentityComplement varchar(300)=''
declare @pNom varchar(300) = 'assoufi'
declare @pPrenom varchar(300) ='rachida'
declare @pPassword varchar(max) ='rachida'

declare @pAppellationId int = 2
declare @pDateOfBirthday varchar(500) = '01/01/1900'
declare @pAddress1 varchar(300)=''
declare @pAddress2 varchar(300)=''
declare @pAddress3 varchar(300)=''
declare @pAddress4 varchar(300)=''
declare @pPostalCode varchar(10)=''
declare @pVille varchar(300)=''
declare @pPays varchar(300)=''
declare @pOperateurId int=5650002
declare @pFiliereid int=9910006


*/

/* Variables locales SQL */
DECLARE @postalTel1 varchar(300)=@unchangedString
DECLARE @postalTelLib1 int=-1
DECLARE @postalTel2 varchar(300)=@unchangedString
DECLARE @postalTelLib2 int=-1
DECLARE @postalTel3 varchar(300)=@unchangedString
DECLARE @postalTelLib3 int=-1
DECLARE @postalTel4 varchar(300)=@unchangedString
DECLARE @postalTelLib4 int=-1
DECLARE @postalTel5 varchar(300)=@unchangedString
DECLARE @postalTelLib5 int=-1
DECLARE @postalTel6 varchar(300)=@unchangedString
DECLARE @postalTelLib6 int=-1
DECLARE @postalTel7 varchar(300)=@unchangedString
DECLARE @postalTelLib7 int=-1

DECLARE @TblTemp table (Email varchar(250),Identite_id int)

IF @checkEmailUnicity = 1 and @pEmail<>''
BEGIN
	insert into @TblTemp
	select top 1 EMail,Identite_id from (
	select 
	case when @pPostalTelEmail =1 then postal_tel1 else '' end +
	case when @pPostalTelEmail =2 then postal_tel2 else '' end +
	case when @pPostalTelEmail =3 then postal_tel3 else '' end +
	case when @pPostalTelEmail =4 then postal_tel4 else '' end +
	case when @pPostalTelEmail =5 then postal_tel5 else '' end +
	case when @pPostalTelEmail =6 then postal_tel6 else '' end +
	case when @pPostalTelEmail =7 then postal_tel7 else '' end
	as email, *
	from identite
	) s
	where email =@pEmail and identite_id <> @pIdentiteId
	--and identite_password = ''
	and FicheSupprimer = 'N'
	order by identite_id desc

END


DECLARE @CountResult int

set @CountResult = (select count(*) from @TblTemp)

IF @CountResult >0 
	begin
		--identite existe déjà
		select -1  as nextIdentityId
	
	end

IF @CountResult =0 
	BEGIN

	
		if @pPostalTelEmail = 1
		begin
			set @PostalTel1 = @pemail
			set @postalTelLib1 = @plibelleEmail
		end 
		if @pPostalTelEmail = 2
		begin
			set @PostalTel2 = @pemail
			set @postalTelLib2 = @plibelleEmail
		end 
		if @pPostalTelEmail = 3
		begin
			set @PostalTel3 = @pemail
			set @postalTelLib3 = @plibelleEmail
		end 
		if @pPostalTelEmail = 4 
		begin
			set @PostalTel4 = @pemail
			set @postalTelLib4 = @plibelleEmail
		end 
		if @pPostalTelEmail = 5 
		begin
			set @PostalTel5 = @pemail
			set @postalTelLib5 = @plibelleEmail
		end 
		if @pPostalTelEmail = 6 
		begin
			set @PostalTel6 = @pemail
			set @postalTelLib6 = @plibelleEmail
		end 
		if @pPostalTelEmail = 7
		begin
			set @PostalTel7 = @pemail
			set @postalTelLib7 = @plibelleEmail
		end 

		if @pPostalTelFax = 1
		begin
			set @PostalTel1 = @pFax
			set @postalTelLib1 = @plibelleFax
		end 
		if @pPostalTelFax = 2
		begin
			set @PostalTel2 = @pFax
			set @postalTelLib2 = @plibelleFax
		end 
		if @pPostalTelFax = 3
		begin
			set @PostalTel3 = @pFax
			set @postalTelLib3 = @plibelleFax
		end 
		if @pPostalTelFax = 4
		begin
			set @PostalTel4 = @pFax
			set @postalTelLib4 = @plibelleFax
		end 
		if @pPostalTelFax = 5
		begin
			set @PostalTel5 = @pFax
			set @postalTelLib5 = @plibelleFax
		end 
		if @pPostalTelFax = 6
		begin
			set @PostalTel6 = @pFax
			set @postalTelLib6 = @plibelleFax
		end 
		if @pPostalTelFax = 7
		begin
			set @PostalTel7 = @pFax
			set @postalTelLib7 = @plibelleFax
		end 

		if @pPostalTelPhone = 1
		begin
			set @PostalTel1 = @pPhoneNumber
			set @postalTelLib1 = @plibelleFax
		end 
		if @pPostalTelPhone = 2
		begin
			set @PostalTel2 = @pPhoneNumber
			set @postalTelLib2 = @plibelleFax
		end 
		if @pPostalTelPhone = 3
		begin
			set @PostalTel3 = @pPhoneNumber
			set @postalTelLib3 = @plibelleFax
		end 
		if @pPostalTelPhone = 4
		begin
			set @PostalTel4 = @pPhoneNumber
			set @postalTelLib4 = @plibelleFax
		end 
		if @pPostalTelPhone = 5
		begin
			set @PostalTel5 = @pPhoneNumber
			set @postalTelLib5 = @plibelleFax
		end 
		if @pPostalTelPhone = 6
		begin
			set @PostalTel6 = @pPhoneNumber
			set @postalTelLib6 = @plibelleFax
		end 
		if @pPostalTelPhone = 7
		begin
			set @PostalTel7 = @pPhoneNumber
			set @postalTelLib7 = @plibelleFax
		end 

		if @pPostalTelMobilePhone = 1
		begin
			set @PostalTel1 = @pMobilePhoneNumber
			set @postalTelLib1 = @plibelleFax
		end 
		if @pPostalTelMobilePhone = 2
		begin
			set @PostalTel2 = @pMobilePhoneNumber
			set @postalTelLib2 = @plibelleFax
		end 
		if @pPostalTelMobilePhone = 3
		begin
			set @PostalTel3 = @pMobilePhoneNumber
			set @postalTelLib3 = @plibelleFax
		end 
		if @pPostalTelMobilePhone = 4
		begin
			set @PostalTel4 = @pMobilePhoneNumber
			set @postalTelLib4 = @plibelleFax
		end 
		if @pPostalTelMobilePhone = 5
		begin
			set @PostalTel5 = @pMobilePhoneNumber
			set @postalTelLib5 = @plibelleFax
		end 
		if @pPostalTelMobilePhone = 6
		begin
			set @PostalTel6 = @pMobilePhoneNumber
			set @postalTelLib6 = @plibelleFax
		end 
		if @pPostalTelMobilePhone = 7
		begin
			set @PostalTel7 = @pMobilePhoneNumber
			set @postalTelLib7 = @plibelleFax
		END 
	END



IF @CountResult =0 
	BEGIN
			UPDATE identite 
			SET 
			identite_password =		IIF(@pPassword <> @unchangedString, @pPassword, identite_password),			
			identite_nom =			IIF(@pnom <> @unchangedString, @pnom, identite_nom),
			identite_prenom =		IIF(@pPrenom <> @unchangedString, @pPrenom, identite_prenom),
			postal_rue1 =			IIF(@pAddress1 <> @unchangedString, @pAddress1, postal_rue1),
			postal_rue2 =			IIF(@pAddress2 <> @unchangedString, @pAddress2, postal_rue2),
			postal_rue3 =			IIF(@pAddress3 <> @unchangedString, @pAddress3, postal_rue3),
			postal_rue4 =			IIF(@pAddress4 <> @unchangedString, @pAddress4, postal_rue4),
			postal_cp =				IIF(@pPostalCode <> @unchangedString, @pPostalCode, postal_cp),
			postal_ville =			IIF(@pVille <> @unchangedString, @pVille, postal_ville),
			postal_pays =			IIF(@pPays <> @unchangedString, @pPays, postal_pays),
			postal_region =			IIF(@pPays <> @unchangedString, @pRegion, postal_region),

			postal_tel5 =			IIF(@PostalTel5 <> @unchangedString, @PostalTel5, postal_tel5),
			postal_tel6 =			IIF(@PostalTel6 <> @unchangedString, @PostalTel6, postal_tel6),

			identite_date_naissance = iif(@pDOB <> @unchangedDateTime, @pDOB, identite_date_naissance),
			appellation_id =		iif(@pAppellationId <> @unchangedInt, @pAppellationId, appellation_id),


			identite_date_modification = GETDATE()
						
			WHERE identite_id = @pIdentiteId

		SELECT @pidentiteId

	END


