﻿DECLARE @const_placementlibre int; set @const_placementlibre =32;
DECLARE @const_priseauto int; set @const_priseauto =16;
DECLARE @const_vueplacement int; set @const_vueplacement =8;
DECLARE @const_choixsurplan int; set @const_choixsurplan =1;


declare @myOffre int
set @myOffre = @pofferId

declare @myEventId int
set @myEventId = @peventId

DECLARE @DateDeb as datetime
DECLARE @DateFin as datetime 
SET @DateDeb = @dateFrom
SET @DateFin = @dateTo


declare @manif_id int
declare @sql varchar(max)

--CREATE TABLE #myManifs (manif_id int)
create TABLE #myManifsGpIds (manif_id int, gestion_place_id int)
CREATE TABLE #GrilleTarif (session_id int,manif_id int, gestion_place_id int, type_tarif_id int, 
type_tarif_nom VARCHAR(50),
categ_id int, 
vts_grille1 decimal(18,2) ,
vts_grille2 decimal(18,2) ,
vts_grille3 decimal(18,2) ,
vts_grille4 decimal(18,2) ,
vts_grille5 decimal(18,2) ,
vts_grille6 decimal(18,2) ,
vts_grille7 decimal(18,2) ,
vts_grille8 decimal(18,2) ,
vts_grille9 decimal(18,2) ,
vts_grille10 decimal(18,2) ,
vts_valeur decimal(18,2) ,
vts_id int
);


if (@myEventId=0)
	if (@myOffre=0)
		begin
			insert into #myManifsGpIds select distinct manif_id, gestion_place_id from gestion_place gp
				inner join seance s on s.seance_id=gp.seance_id 
			where /*isvalide=1 and*/
				s.seance_date_deb>getdate() and
				isContrainteIdentite=0
		end
	else
		begin
			insert into #myManifsGpIds select distinct manif_id, gp.gestion_place_id from gestion_place gp
			inner join offre_gestion_place gpo on gpo.gestion_place_id = gp.gestion_place_id 
			inner join seance s on s.seance_id=gp.seance_id 

			where /*gp.isvalide=1*/  gpo.offre_id = @myOffre and s.seance_date_deb>getdate()
		end
else
	if (@myOffre=0)
		begin
			insert into #myManifsGpIds select distinct manif_id, gestion_place_id from gestion_place gp 
			inner join seance s on s.seance_id=gp.seance_id 
			where /*isvalide=1 and*/
				isContrainteIdentite=0 and manif_id=@myEventId and s.seance_date_deb>getdate()
		end
	else
		begin
			insert into #myManifsGpIds select distinct manif_id, gp.gestion_place_id from gestion_place gp
			inner join offre_gestion_place gpo on gpo.gestion_place_id = gp.gestion_place_id 
			inner join seance s on s.seance_id=gp.seance_id 
			where /*gp.isvalide=1*/ gpo.offre_id = @myOffre and manif_id=@myEventId and s.seance_date_deb>getdate()
		end

		/* supp les gestion_place qui ne sont pas dans le bon intervalle de date */
		delete #myManifsGpIds WHERE gestion_place_id NOT IN (
			select gestion_place_id FROM gestion_place gp 
			INNER JOIN seance s on s.seance_id = gp.seance_id
			where (seance_date_deb between @DateDeb and @DateFin OR seance_date_fin between @DateDeb and @DateFin)
		)

DECLARE cur_manifs CURSOR SCROLL FOR
	select distinct manif_id from #myManifsGpIds 

OPEN cur_manifs;

--select * from gestion_place

FETCH NEXT FROM cur_manifs INTO @manif_id
WHILE @@FETCH_STATUS = 0
BEGIN
	--SELECT @manif_id

		set @sql = 'INSERT INTO #GrilleTarif SELECT vts.seance_id, ' +  convert(varchar,@manif_id) + ' as manif_id, gp.gestion_place_id, tt.type_tarif_id, type_tarif_nom,
	vts.categ_id, vts_grille1 ,
	vts_grille2 ,
	vts_grille3 ,
	vts_grille4 ,
	vts_grille5 ,
	vts_grille6 ,
	vts_grille7 ,
	vts_grille8 ,
	vts_grille9 ,
	vts_grille10,
	vts_valeur,
	vts_id
	FROM valeur_tarif_stock' + convert(varchar,@manif_id) + ' vts' + CHAR(13) +
	'INNER JOIN type_tarif tt on tt.type_tarif_id = vts.type_tarif_id ' +CHAR(13) + 
	'INNER JOIN gestion_place gp on vts.categ_id = gp.categ_id and gp.type_tarif_id =vts.type_tarif_id and vts.seance_id=gp.seance_id ' +CHAR(13) +

	'INNER JOIN #myManifsGpIds gpFiltree ON gpFiltree.gestion_place_id=gp.gestion_place_id '

	set @SQL+= CHAR(9) + 'WHERE vts_v=(SELECT MAX(vts_v) FROM valeur_tarif_stock' +  convert(varchar,@manif_id) + ' vts2 
			WHERE 
			vts2.tarif_logique_id=vts.tarif_logique_id
			and vts2.seance_id=vts.seance_id
			and vts2.categ_id= vts.categ_id
			and vts2.type_tarif_id= vts.type_tarif_id)
			AND vts_grille1>=0 ' +CHAR(13)


    set @sql += CHAR(13) + 'order by seance_id, vts.categ_id, vts.type_tarif_id'

	 			print @sql
	exec (@sql)


	FETCH NEXT FROM cur_manifs INTO @manif_id
END

CLOSE cur_manifs
DEALLOCATE cur_manifs


SELECT m.manifestation_nom as event_name, m.manifestation_id as event_id, m.manifestation_code as event_code, s.seance_id as session_id, 
s.seance_date_deb as date_start, 
s.seance_date_fin as date_end,
l.lieu_id as place_id, l.lieu_nom as place_name, l.lieu_code as place_code,
gp.dispo as seats_availables, gp.placestotal as seats_total ,
cat.categ_id as categ_id, cat.categ_nom as categ_name, cat.pref_affichage as cat_pref_affichage, 
tt.type_tarif_id as price_id, tt.type_tarif_nom as price_name, tt.type_tarif_code as price_code
, 
tt.pref_affichage as tarif_pref_affichage,

convert(int,gp.gestion_place_id) as rule_id,


gp.isvalide, 
gp.nb_max,
gp.nb_min,
notnumbered = case WHEN gp.prise_place is null
then 0 else
CASE WHEN (gp.prise_place & @const_placementlibre)=0 THEN 0 ELSE 1 END
end
,priseauto = CASE WHEN (gp.prise_place & @const_priseauto)=0 THEN 0 ELSE 1 END
,vueplacement = CASE WHEN (gp.prise_place & @const_vueplacement)=0 THEN 0 ELSE 1 END
,choixplan = CASE WHEN (gp.prise_place & @const_choixsurplan)=0 THEN 0 ELSE 1 END,

case when gp.date_deb_validite_type is null then date_deb_validite /* date fixe */
else
	/* date glissante */
	case when gp.date_deb_validite_type = 1 /* jours*/
		then dateadd(day, -date_deb_validite_int, s.seance_date_deb)
		else
			case when gp.date_deb_validite_type = 2 /* heures */
			then dateadd(hour, -date_deb_validite_int, s.seance_date_deb)
			end
		end
end as date_start_validity,
case when gp.date_fin_validite_type is null then date_fin_validite /* date fixe */
else
	/* date glissante */
	case when gp.date_fin_validite_type = 1 /* jours*/
		then dateadd(day, -date_fin_validite_int, s.seance_date_deb)
		else
			case when gp.date_fin_validite_type = 2 /* heures */
			then dateadd(hour, -date_fin_validite_int, s.seance_date_deb)
			end
		end
end as date_end_validity,


convert(int,(gt.vts_grille1 + gt.vts_grille2 +
                           case  when modecol4='REMISE' then gt.vts_grille4 
                        				when modecol4='TAXE' then - gt.vts_grille4
                        				else 0 END +
                         case  when modecol5='REMISE'  then gt.vts_grille5
                        				when modecol5='TAXE' then - gt.vts_grille5
                        				else 0 END +
                         case  when modecol6='REMISE' then gt.vts_grille6
                        				when modecol6='TAXE' then - gt.vts_grille6
                        				else 0 END +
                         case  when modecol7='REMISE'  then gt.vts_grille7 
                        				when modecol7='TAXE' then - gt.vts_grille7
                        				else 0 END +
                         case  when modecol8='REMISE'  then gt.vts_grille8 
                        				when modecol8='TAXE' then - gt.vts_grille8
                        				else 0 END +
                         case  when modecol9='REMISE' then gt.vts_grille9 
                        				when modecol9='TAXE' then - gt.vts_grille9
                        				else 0 END +
                         case  when modecol10='REMISE'  then gt.vts_grille10 
                        				when modecol10='TAXE' then - gt.vts_grille10
                        				else 0 END) * 100) as Amount,
						convert(int,gt.vts_grille2*100) as Fees,
                        convert(int,gt.vts_valeur * 100) as Value

--gt.*, gp.*  
FROM #GrilleTarif gt 
INNER JOIN seance s ON s.seance_id = gt.session_id
INNER JOIN lieu l on s.lieu_id = l.lieu_id
INNER JOIN manifestation m ON m.manifestation_id=s.manifestation_id 
INNER JOIN gestion_place gp ON gp.gestion_place_id=gt.gestion_place_id
INNER JOIN categorie cat ON cat.categ_id = gp.categ_id
INNER JOIN type_tarif tt ON tt.type_tarif_id = gp.type_tarif_id
INNER JOIN structure on 1=1
order by s.manifestation_id, s.seance_date_deb, cat.pref_affichage, tt.pref_affichage

drop table #myManifsGpIds
drop table #GrilleTarif


