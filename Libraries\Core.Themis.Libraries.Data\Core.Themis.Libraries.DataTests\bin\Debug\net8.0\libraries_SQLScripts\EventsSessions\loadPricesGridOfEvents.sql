/* grille de tarif avec tva, hors frais, frais */







CREATE TABLE #myResult (eventId int, 
--manifNom varchar(50),
sessionId int, 
type_tarifid int,
type_tarifnom varchar(50),
categId int,
categnom varchar(50),
amountTTCHF decimal(18,2),
tvaHFnom varchar(50),
tvaHFTaux decimal(18,2),
amountTTCF decimal(18,2),
tvaFnom varchar(50),
tvaFTaux decimal(18,2)

)
declare @manifId int

INSERT INTO #myResult 

SELECT distinct s.manifestation_id as eventId, 
	 s.seance_id as sessionId, -- seance_verrouiller,
	 tt.type_tarif_id, tt.type_tarif_nom,
	 cat.categ_id, cat.categ_nom,

	 vts_grille1 as amountTTCHorsFrais,
	 tva1.tva_libelle as tva_libelleHF, tva1.tva_taux as tva_tauxHF,

	 vts_grille2 as amountTTCFrais,
	 tva2.tva_libelle as tva_libelleF, tva2.tva_taux as tva_tauxH


	FROM seance s

	INNER JOIN valeur_tarif_stock10 vts on s.seance_Id = vts.seance_id
	INNER JOIN type_tarif tt ON tt.type_tarif_id = vts.type_tarif_id
	INNER JOIN categorie cat on cat.categ_id = vts.categ_id
	LEFT JOIN TVA tva1 on TVA1.tva_id = s.taux_tva1_id
	LEFT JOIN TVA tva2 on TVA2.tva_id = s.taux_tva2_id
	WHERE s.manifestation_id =10

	AND vts_v =(	SELECT MAX(vts_v) FROM valeur_tarif_stock10 vts2
					WHERE vts2.tarif_logique_id=vts.tarif_logique_id
					and vts2.seance_id=vts.seance_id
					and vts2.categ_id= vts.categ_id
					and vts2.type_tarif_id= vts.type_tarif_id
		) and vts.vts_grille1>=0


	ORDER BY s.manifestation_id, s.seance_id, cat.categ_id, tt.type_tarif_id asc


SELECT * FROM #myResult

DROP TABLE #myResult
