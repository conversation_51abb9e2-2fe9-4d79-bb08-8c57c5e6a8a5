﻿declare     
@START_NUMBER int,
@END_NUMBER int,
--paramètres locales
@datestart datetime,
@dateend datetime,
@intervalleMinute int,
@offset int,
@lstSaleChannels varchar(max),
@SQL varchar(max)


/*

declare
@pStructureid varchar(3),
@pStartDate varchar(20),
@pEndDate varchar(20),
@pLstSaleChannels varchar(max),
@pOrderId int,
@pPage int,
@pItemsPerPage int,
@pDisplayMode varchar(20)


set @pLstSaleChannels = 'all'
set @pStartDate = '15/09/2020'
set @pEndDate = '21/09/2020'
set @pStructureid = 991
set @pOrderId = 0



set @pItemsPerPage = 5
set @pPage = 0
*/




--défini des valeurs par défaut si les dates ne sont pas renseignées
set @datestart = DATEADD(DAY, -7, getdate()) -- FORMAT(GETDATE()-7, 'dd/MM/yyyy') --
set @dateend = GETDATE()

set @intervalleMinute=1

declare @TableTmp table (filiere_id int)


if @pLstSaleChannels <> '' and @pLstSaleChannels <> 'all'
BEGIN
	set @SQL = 'select distinct filiere_id from OpinionOrderForms where filiere_id in ('+ @pLstSaleChannels +')'
	insert into @TableTmp exec(@SQL)
	set @lstSaleChannels = @pLstSaleChannels
END


if @pLstSaleChannels = '' or @pLstSaleChannels = 'all'
BEGIN
	insert into @TableTmp
	select distinct(filiere_id) from OpinionOrderForms where filiere_id is not null

		SELECT 
    @lstSaleChannels = 
    STUFF (
        (SELECT   
                ',' +convert(varchar(max),filiere_id)   
        FROM @TableTmp
        FOR XML PATH('')), 1, 1, ''
    )
FROM @TableTmp GROUP by filiere_id
END

if @pOrderId <> '' OR @pOrderId > 0
BEGIN
	set @pDisplayMode = 'all'
	set @datestart = DATEADD(DAY, -7, getdate()) -- FORMAT(GETDATE()-7, 'dd/MM/yyyy') --
	set @dateend = GETDATE()

END

if @pDisplayMode = 'all' --mode affichage == tout
	BEGIN
		set @pItemsPerPage = 5
		set @pPage = 0
	END
else -- mode pagination
	BEGIN
		set @offset = @pPage * @pItemsPerPage

	END

if @pStartDate <> ''
BEGIN
	set @datestart= @pStartDate
END


if @pEndDate <> ''
BEGIN
	set @dateend =@pEndDate
END

/*
if @pOrderId <> '' OR @pOrderId > 0
BEGIN
	set @pLstSaleChannels = ''
	set @datestart = DATEADD(DAY, -7, getdate()) -- FORMAT(GETDATE()-7, 'dd/MM/yyyy') --
	set @dateend = GETDATE()

END
*/


set @SQL = 'select oof.form_id as order_id_form, structure_id, order_id, basket_id,
		filiere_id, comment, reponse_id, question_id, score, id as opinion_order_response_id, code as question_code, date_response
		from OpinionOrderForms oof
		inner join OpinionOrderFormsResponses resp on resp.form_id = oof.form_id
		inner join OpinionOrderQuestions q on q.id = resp.question_id
		where structure_id='+ CONVERT(varchar(10), @pStructureid)  +''
		

		if @pOrderId <> '' OR @pOrderId > 0
			BEGIN
				set @SQL += ' and order_id = '+ convert(varchar(max), @pOrderId) 
			END			
		ELSE
			BEGIN
				set @SQL += ' and date_response  between  '''+convert(varchar(max),FORMAT(@datestart, 'dd/MM/yyyy 00:00:00') )+''' and '''+ convert(varchar(max),FORMAT(@dateend, 'dd/MM/yyyy 23:59:59')) +''''
			END


		if @lstSaleChannels <> ''
		BEGIN
			set @SQL += ' and oof.filiere_id in  ( '+@lstSaleChannels+' )'
		END


		if @pDisplayMode <> 'all'
		BEGIN

		set @SQL += 'and order_id in (
				select distinct order_id from OpinionOrderForms oof2
				inner join OpinionOrderFormsResponses resp on resp.form_id = oof2.form_id
				where structure_id ='+ CONVERT(varchar(10), @pStructureid)  +' 				
				and date_response  between  '''+convert(varchar(max),FORMAT(@datestart, 'dd/MM/yyyy 00:00:00') )+''' and '''+ convert(varchar(max),FORMAT(@dateend, 'dd/MM/yyyy 23:59:59')) +'''
				and oof2.filiere_id in  ('+@lstSaleChannels +')
				order by order_id desc
				OFFSET '+ CONVERT(varchar(100), @offset) +' ROWS FETCH NEXT '+ CONVERT(varchar(100), @pItemsPerPage) +' ROWS ONLY
				)'
		END
		set @SQL += 'order by date_response desc'

		--print(@SQL)
		exec(@SQL)




	-- nombre total de lignes
	
	select  count(distinct order_id) as nb_orders_rows
		from OpinionOrderForms oof
		inner join OpinionOrderFormsResponses resp on resp.form_id = oof.form_id
		inner join OpinionOrderQuestions q on q.id = resp.question_id
		where structure_id=@pStructureid and
		date_response  between  convert(varchar(max),FORMAT(@datestart, 'dd/MM/yyyy 00:00:00') ) and convert(varchar(max),FORMAT(@dateend, 'dd/MM/yyyy 23:59:59'))
		--date_response>=@datestart and date_response<=@dateend
		and oof.filiere_id in  (select filiere_id from @TableTmp )
		

