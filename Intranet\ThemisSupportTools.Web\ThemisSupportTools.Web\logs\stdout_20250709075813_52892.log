info: Microsoft.Hosting.Lifetime[0]
      Application started. Press Ctrl+C to shut down.
info: Microsoft.Hosting.Lifetime[0]
      Hosting environment: local
info: Microsoft.Hosting.Lifetime[0]
      Content root path: D:\WORK\TST_LD\ThemisSupportTools_LD\Intranet\ThemisSupportTools.Web\ThemisSupportTools.Web
Structure sélectionnée ID: 3
Nombre total de partenaires: 63
Recherche avec ID: '3' et '0003'
Partenaire trouvé: DEMO
Partenaire trouvé: RODRIGUE
Partenaire trouvé: PARTDEMO
Partenaire trouvé: PARTENAIREATU
Partenaire trouvé: Maiga
Partenaire trouvé: partmodal
Partenaire trouvé: ttdyjytj
Nombre de partenaires trouvés: 4
Structure sélectionnée ID: 3
Nombre total de partenaires: 63
Recherche avec ID: '3' et '0003'
Partenaire trouvé: DEMO
Partenaire trouvé: RODRIGUE
Partenaire trouvé: PARTDEMO
Partenaire trouvé: PARTENAIREATU
Partenaire trouvé: Maiga
Partenaire trouvé: partmodal
Partenaire trouvé: ttdyjytj
Nombre de partenaires trouvés: 1
Structure sélectionnée ID: 3
Nombre total de partenaires: 63
Recherche avec ID: '3' et '0003'
Partenaire trouvé: DEMO
Partenaire trouvé: RODRIGUE
Partenaire trouvé: PARTDEMO
Partenaire trouvé: PARTENAIREATU
Partenaire trouvé: Maiga
Partenaire trouvé: partmodal
Partenaire trouvé: ttdyjytj
Nombre de partenaires trouvés: 4
Structure sélectionnée ID: 3
Nombre total de partenaires: 63
Recherche avec ID: '3' et '0003'
Partenaire trouvé: DEMO
Partenaire trouvé: RODRIGUE
Partenaire trouvé: PARTDEMO
Partenaire trouvé: PARTENAIREATU
Partenaire trouvé: Maiga
Partenaire trouvé: partmodal
Partenaire trouvé: ttdyjytj
Nombre de partenaires trouvés: 7
fail: Microsoft.AspNetCore.Diagnostics.ExceptionHandlerMiddleware[1]
      An unhandled exception has occurred while executing the request.
      System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
         at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
         at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
         at System.Data.SqlClient.SqlConnection.Open()
         at Core.Themis.Libraries.Data.Repositories.Common.GenericRepository`1.GetAll() in D:\WORK\TST_LD\ThemisSupportTools_LD\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\Repositories\Common\GenericRepository.cs:line 65
         at Core.Themis.Libraries.Data.Repositories.Common.GenericRepository`1.GetAllCustomEntities() in D:\WORK\TST_LD\ThemisSupportTools_LD\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\Repositories\Common\GenericRepository.cs:line 723
         at Core.Themis.Libraries.BLL.PartnerManager.GetAllPartners() in D:\WORK\TST_LD\ThemisSupportTools_LD\Libraries\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLL\PartnerManager.cs:line 931
         at ThemisSupportTools.Web.Components.Pages.Modules.Partners.Partners.LoadPartners() in D:\WORK\TST_LD\ThemisSupportTools_LD\Intranet\ThemisSupportTools.Web\ThemisSupportTools.Web\Components\Pages\Modules\Partners\Partners.razor.cs:line 154
         at ThemisSupportTools.Web.Components.Pages.Modules.Partners.Partners.OnInitializedAsync() in D:\WORK\TST_LD\ThemisSupportTools_LD\Intranet\ThemisSupportTools.Web\ThemisSupportTools.Web\Components\Pages\Modules\Partners\Partners.razor.cs:line 42
         at Microsoft.AspNetCore.Components.ComponentBase.RunInitAndSetParametersAsync()
         at Microsoft.AspNetCore.Components.Rendering.ComponentState.SupplyCombinedParameters(ParameterView directAndCascadingParameters)
         at Microsoft.AspNetCore.Components.Rendering.ComponentState.SetDirectParameters(ParameterView parameters)
         at Microsoft.AspNetCore.C