DECLARE @groupCarteCadeau int = 9
DECLARE @groupBoutique int = 99
DECLARE @groupSans int = 0
DECLARE @groupCAdh int = 3
DECLARE @groupResto int = 2
DECLARE @groupMO int =6
DECLARE @groupAssurance int = 7
DECLARE @groupReservation int = 12
DECLARE @groupFraisEnvoi int = 1


Declare @TblFunctions table (BlockFunctionId int, FunctionId int, FunctionName varchar(250) )
[FOREACH.INSERTINTO]

/*
INSERT INTO @TblFunctions VALUES (3, 2, 'Products_Family')
INSERT INTO @TblFunctions VALUES (5, 3, 'Products_Subfamily')
INSERT INTO @TblFunctions VALUES (36, 19, 'Select_Products_List_Multiple')
INSERT INTO @TblFunctions VALUES (34, 20, 'Select_Products_Family_List_Multiple')
INSERT INTO @TblFunctions VALUES (35, 21, 'Select_Products_Subfamily_List_Multiple')
INSERT INTO @TblFunctions VALUES (31, 17, 'Select_Front_MsgIntro')
INSERT INTO @TblFunctions VALUES (32, 18, 'Select_Front_MsgIntro')
INSERT INTO @TblFunctions VALUES (39, 16, 'Input_Items_Max_To_Display')
INSERT INTO @TblFunctions VALUES (37, 16, 'Input_Stock_Filling_Min')
INSERT INTO @TblFunctions VALUES (38, 16, 'Input_Stock_Filling_Max')
*/
/*
declare @pBlockEmplacement int = 287
declare @pLangCode varchar(5) = 'fr'
declare @pFiliereId int =  5650004
*/

----- VA 26/09/2023  -- Gestion des stocks. Insertion dans un table temp les stocks des produits selon la filiere . Table réutilisée dans la requête finale pour les produits boutique.
declare @TmpStock table ( produit_id int, Stock int )
DECLARE @colStock varchar(2) = '0'
declare @SqlStock varchar(max)
--set @pFiliereId = 5650004
SELECT @colStock = replace(droit_boutique_code,'DEPOT_FILIERES_','') FROM Produit_Droit_Boutique WHERE droit_boutique_code LIKE
	'DEPOT_FILIERES_%' and droit_boutique_champ like '%' + convert(varchar(100),@pFiliereId) + '%'
if @colStock >'0'
	begin
		SET @SqlStock = 'Select produit_id , restant_' + @colStock + ' from produit_stock '
		insert into @TmpStock exec @SqlStock
	end
------ fin VA 26/09/2023 





declare @langue_id int = (select langue_id from langue where langue_code = @pLangCode)



Declare @TblDispoProduits table (Produit_Nom varchar(250)
                                                      , Produit_id int 
                                                      , produit_pref_affichage int
                                                      , Produit_Famille_id int
                                                      , Produit_Famille_Nom varchar(250)
                                                      , Produit_Sous_Famille_id int 
                                                      , Produit_Sous_Famille_nom varchar(250)
                                                      , StockDispo int 
                                                      , StockTotal int
                                                      )




declare @BlockFunction_ID int 
Declare @SQL varchar(max)


Set @BlockFunction_ID = (select BlockFunctionId from @TblFunctions where FunctionName = 'Input_Items_Max_To_Display')
Declare @NbTop int = ISNULL((select BlockcUserConfig_Value from HomeModular_BlockUserConfig 
                                                                                                                                where BlockFunction_ID =@BlockFunction_ID and BlockEmplacement_ID= @pBlockEmplacement ), 0)  --- Par défaut 8, mais pourra être changé


declare @ProfilAcheteurId int =0 --- Normalement, on ne gère pas le profil d'acheteur, mais à penser. donc 0 par défaut


Set @BlockFunction_ID = (select BlockFunctionId from @TblFunctions where FunctionName = 'Input_Stock_Filling_Min')
Declare @FiltreRemplissageMin int = (select BlockcUserConfig_Value from HomeModular_BlockUserConfig 
                                                                                                                                where BlockFunction_ID =@BlockFunction_ID and BlockEmplacement_ID= @pBlockEmplacement ) --- Filtre à définir à l'appel du SQL 


Set @BlockFunction_ID = (select BlockFunctionId from @TblFunctions where FunctionName = 'Input_Stock_Filling_Max')
Declare @FiltreRemplissageMax int = (select BlockcUserConfig_Value from HomeModular_BlockUserConfig 
                                                                                                                                where BlockFunction_ID =@BlockFunction_ID and BlockEmplacement_ID= @pBlockEmplacement ) --- Filtre à définir à l'appel du SQL 




--- Attention, chaque filtre est additionnel

set @BlockFunction_ID = (select BlockFunctionId from @TblFunctions where FunctionName = 'Select_Products_List_Multiple')
declare @FiltreProduit varchar(max) =  (select BlockcUserConfig_Value from HomeModular_BlockUserConfig 
                                                                                                                                where BlockFunction_ID in (@BlockFunction_ID) and BlockEmplacement_ID= @pBlockEmplacement )  --'1;2;3;4;7;8;9;10;11;12;13;14' --- Filtre à définir à l'appel du SQL  Select_Events_List_Multiple

set @BlockFunction_ID = (select BlockFunctionId from @TblFunctions where FunctionName = 'Select_Products_Family_List_Multiple')
Declare @FiltreFamilleProduit varchar(max) =  (select BlockcUserConfig_Value from HomeModular_BlockUserConfig 
                                                                                                                         where BlockFunction_ID in (@BlockFunction_ID) and BlockEmplacement_ID= @pBlockEmplacement )---'1;2;3;4' --- Filtre à définir à l'appel du SQL ---- Sur Thémis Manifestation_Groupe_genre = Genre Select_Events_Genre_List_Multiple

set @BlockFunction_ID = (select BlockFunctionId from @TblFunctions where FunctionName = 'Select_Products_Subfamily_List_Multiple')
Declare @FiltreSousFamilleProduit Varchar(max) = (select BlockcUserConfig_Value from HomeModular_BlockUserConfig 
                                                                                                                         where BlockFunction_ID in (@BlockFunction_ID) and BlockEmplacement_ID= @pBlockEmplacement )---'1;2' --- --- Filtre à définir à l'appel du SQL ---- Sur Thémis Manifestation_Genre = Sous_Genre 
---- 


--- Produits 
declare @TmpFiltreProduit table (Val int) 
if @FiltreProduit = '' --Pas de filtre
       begin 
       
             Set @SQL = 'Select produit_id from produit p where p.internet = 1 AND
                           p.groupe_id not in ('+Convert(VARCHAR, @groupMO)+', '+ Convert(VARCHAR, @groupAssurance)+', '+ Convert(VARCHAR, @groupReservation)+', '+ Convert(VARCHAR, @groupFraisEnvoi)+') 
                           and p.produit_anu <> '+Convert(VARCHAR, @groupBoutique) 
			print @SQL
       end 
if @FiltreProduit > '' 
       begin 
             Set @SQL = 'Select produit_id  from produit  where produit_id in (' + replace(@FiltreProduit,';',',') +  ') ' 
			 print @SQL
       end 


insert into @TmpFiltreProduit exec (@SQL) 


--remplace le -1 par 0 
select @FiltreFamilleProduit = replace(@FiltreFamilleProduit, '-1','0' ) 


--- Filtre Famille des produits
declare @TmpFiltreFamille table (Val int) 
if @FiltreFamilleProduit = '' 
       begin 
             Set @SQL = 'select produit_id FROM Produit_Lien_Sous_Famille'      
			 print @SQL
       end 
if @FiltreFamilleProduit > '' 
       begin 
             declare @countNegativeValue int = (select count(*) from splitstring(@FiltreFamilleProduit, ';') where  sign(Name) = -1 )
             declare @countPositiveValue int = (select count(*) from splitstring(@FiltreFamilleProduit, ';') where  sign(Name) = 1 )
             
             if @countNegativeValue > 0 and @countPositiveValue > 0 -- si on a des valeurs négative et positive ==> familles + produits sans famille
             BEGIN
                           Set @SQL = 'select produit_id from Produit_Lien_Sous_Famille plsf where Produit_Lien_Famille_ID in  (select replace(name, ''-'','''' ) from splitstring('''+@FiltreFamilleProduit+''', '';'') where  sign(Name) = 1)'
                                  set    @SQL += 'union '
                           set    @SQL += 'select produit_id from produit where groupe_id in(select replace(name, ''-'','''' ) from splitstring('''+@FiltreFamilleProduit+''', '';'') where  sign(Name) = -1 )'

                    print @SQL
             END
             ELSE IF @countNegativeValue = 0 and @countPositiveValue > 0 -- si on a des valeurs toutes positives ==> familles
             BEGIN
                    Set @SQL = 'select produit_id FROM Produit_Lien_Sous_Famille where Produit_Famille_ID in (' + replace(@FiltreFamilleProduit,';',',') +  ') '
					print @SQL
             END
             ELSE IF @countNegativeValue > 0 and @countPositiveValue = 0 -- si on a des valeurs toutes négatives ==> produits sans famille
             BEGIN
                    Set @SQL = 'select produit_id from produit where groupe_id in( select replace(name, ''-'','''' ) from splitstring('''+@FiltreFamilleProduit+''', '';'') ) and internet = 1 ' 
                    print @SQL
             END

       end 

insert into @TmpFiltreFamille exec (@SQL) 


--select @FiltreFamilleProduit as FiltreFamilleProduit

--select @FiltreSousFamilleProduit as FiltreSousFamilleProduit
--- Filtre sous Famille des produits
declare @TmpFiltreSousFamille table (Val int) 
if @FiltreSousFamilleProduit = '' 
       begin 
             Set @SQL = 'select produit_id FROM Produit_Lien_Sous_Famille'    
			 print @SQL
       end 


if @FiltreSousFamilleProduit > '' 
       begin 
             Set @SQL = 'select produit_id FROM Produit_Lien_Sous_Famille where produit_sous_famille_id in (' + replace(@FiltreSousFamilleProduit,';',',') +  ') '
			 print @SQL
       end 


insert into @TmpFiltreSousFamille exec (@SQL) 


--select * from @TmpFiltreProduit
--select * from @TmpFiltreFamille
--select * from @TmpFiltreSousFamille


insert into @TblDispoProduits
       select CASE WHEN tp.produit_nom IS null THEN p.produit_nom else tp.produit_nom END AS produitNom,
       p.produit_id as produitId,
       p.pref_affichage as produit_pref_affichage,
       0 as produitFamilleId,
       '' as produitFamilleNom,
       0 as produitSousFamilleId,
       '' as produitsousFamilleNom,
       ps.restant as StockDispo,
       ps.jauge as StockTotal
       FROM produit p 
       LEFT JOIN traduction_produit tp on tp.produit_id = p.produit_id and tp.langue_id = @langue_id
       INNER JOIN produit_stock ps ON ps.produit_id=p.produit_id
       inner join @TmpFiltreProduit fp on fp.val = p.produit_id
       INNER JOIN produit_internet p_i ON p_i.produit_id = p.produit_id
       INNER JOIN @TmpFiltreFamille ffp on ffp.val = p.produit_id
       where p.internet = 1  AND p.groupe_id   not in (@groupMO, @groupAssurance, @groupReservation, @groupFraisEnvoi) and p.groupe_id <> @groupBoutique
      AND ps.manifestation_id =0 AND ps.seance_id=0
	  AND p_i.acces_autonome = 1
UNION
    select CASE WHEN tp.produit_nom IS null THEN p.produit_nom else tp.produit_nom END AS produitNom,
       p.produit_id as produitId,
       p.pref_affichage as produit_pref_affichage,
       0 as produitFamilleId,
       '' as produitFamilleNom,
       0 as produitSousFamilleId,
       '' as produitsousFamilleNom,
       ps.restant as StockDispo,
       ps.jauge as StockTotal
       FROM produit p 
       LEFT JOIN traduction_produit tp on tp.produit_id = p.produit_id and tp.langue_id = @langue_id
       INNER JOIN produit_stock ps ON ps.produit_id=p.produit_id
       inner join @TmpFiltreProduit fp on fp.val = p.produit_id
       INNER JOIN produit_internet p_i ON p_i.produit_id = p.produit_id
       INNER JOIN @TmpFiltreSousFamille fsfp on fsfp.val = p.produit_id
       where p.internet = 1  AND p.groupe_id   not in (@groupMO, @groupAssurance, @groupReservation, @groupFraisEnvoi) and p.groupe_id <> @groupBoutique
      AND ps.manifestation_id =0 AND ps.seance_id=0
	  AND p_i.acces_autonome = 1

UNION

       select CASE WHEN tp.produit_nom IS null THEN p.produit_nom else tp.produit_nom END AS produitNom  ,
       p.produit_id as produitId, 
       p.pref_affichage as produit_pref_affichage,
       pf.Produit_Famille_ID as familleId,
       pf.Produit_Famille_Nom as familleNom,
       psf.Produit_Sous_Famille_ID as sousFamilleId, 
       Produit_Sous_Famille_Nom as sousFamilleNom,
       isnull(stk.Stock,ps.restant) as StockDispo,	   -- VA 26/09/2023
       ps.jauge as StockTotal
FROM Produit_Lien_Sous_Famille plsf
       JOIN Produit_Famille pf on plsf.Produit_Famille_ID = pf.Produit_Famille_ID
       JOIN Produit_Sous_Famille psf on psf.Produit_Sous_Famille_ID = plsf.Produit_Sous_Famille_ID
       JOIN produit p on p.produit_id = plsf.Produit_id
       INNER JOIN produit_stock ps on ps.produit_id = p.produit_id
       LEFT OUTER JOIN traduction_produit tp on  tp.produit_id = p.produit_id and tp.langue_id = @langue_id
       inner join @TmpFiltreProduit fp on fp.val = p.produit_id
       inner join @TmpFiltreFamille ffp on ffp.val = p.produit_id
       inner join @TmpFiltreSousFamille fsfp on fsfp.val = p.produit_id
	   left outer join @TmpStock stk on stk.produit_id = p.produit_id -- VA 26/09/2023
       INNER JOIN produit_internet p_i ON p_i.produit_id = p.produit_id
       where psf.Masquer = 0 and pf.Masquer = 0 
        AND p.groupe_id not in (@groupMO, @groupAssurance, @groupReservation, @groupFraisEnvoi) and p.produit_anu <> @groupBoutique
          AND ps.manifestation_id =0 AND ps.seance_id=0
	  AND p_i.acces_autonome = 1



select top (@NbTop) Produit_id as ProductFeatureId, Produit_Nom as ProductFeatureName, produit_pref_affichage as ProductFeaturePrefAffichage,
produit_Famille_id as ProductFeatureFamilleId, Produit_Famille_Nom as ProductFeatureFamilleName,
Produit_Sous_Famille_id as ProductFeatureSousFamilleId, Produit_Sous_Famille_nom as ProductFeatureSousFamilleName, StockDispo as ProductFeatureStockDispo,
StockTotal as ProductFeatureStockTotal
from @TblDispoProduits 
order by newid(), produit_pref_affichage

