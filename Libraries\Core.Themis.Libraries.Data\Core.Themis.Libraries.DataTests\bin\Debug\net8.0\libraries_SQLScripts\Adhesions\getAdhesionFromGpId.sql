
/*


declare @pPriceId int
set @pPriceId = '5650025'

set @pPriceId = '5650021'

declare @pGpId int

*/


--remonte la formule d'adhésion
SELECT p.produit_ID,Catalog_Libelle,  ac.Adhesion_Catalog_ID, produit_nom, jauge, restant,  convert(INT,(vts_grille1*100+vts_grille2*100)) as amountInCent,
acprop.Propriete_Valeur_Int1, acprop.Propriete_Code, acprop.Propriete_Valeur_Char1, ISNULL(acprop.Propriete_Valeur_Date1, GETDATE()) as Propriete_Valeur_Date1, 
ISNULL(acprop.Propriete_Valeur_Date2,
CASE Propriete_Valeur_Char1 when 'Y' then DATEADD(YEAR, Propriete_Valeur_Int1,  getdate()) 
	when 'M' then DATEADD(MONTH, Propriete_Valeur_Int1,  getdate()) 
	when 'J' then DATEADD(DAY, Propriete_Valeur_Int1,  getdate()) 
	when 'H' then DATEADD(HOUR, Propriete_Valeur_Int1,  getdate()) 
	else getdate() 
end)
as Propriete_Valeur_Date2

FROM Adhesion_Catalog_Type_Tarif actt 
INNER JOIN Adhesion_Catalog ac on ac.Adhesion_Catalog_id = actt.Adhesion_Catalog_id
INNER JOIN Adhesion_Catalog_Propriete acprop on acprop.Adhesion_Catalog_ID = actt.Adhesion_Catalog_ID

INNER JOIN gestion_place gp on gp.type_tarif_id = actt.type_tarif_id 
INNER JOIN offre_gestion_place ogp on ogp.gestion_place_id = gp.gestion_place_id
INNER JOIN offre o on o.offre_id = ogp.offre_id
INNER JOIN adhesion_catalog_offresliees adh_offre on adh_offre.offre_id = o.offre_id and ac.Adhesion_Catalog_ID = adh_offre.adhesion_catalog_id

LEFT OUTER JOIN produit p on ac.Produit_ID = p.produit_id
LEFT OUTER JOIN produit_stock ps on ps.produit_id= p.produit_id


where gp.gestion_place_id  = @pGpId and ac.masquer = 0
and acprop.Propriete_Code  in ('VALIDITE_UTILISATION_TEMPS', 'VALIDITE_UTILISATION_DATES')



/*
select p.produit_ID,Catalog_Libelle,  ac.Adhesion_Catalog_ID, produit_nom, jauge, restant,  convert(INT,(vts_grille1*100+vts_grille2*100)) as amountInCent
from Adhesion_Catalog_Type_Tarif actt 
inner join Adhesion_Catalog ac on ac.Adhesion_Catalog_id = actt.Adhesion_Catalog_id
left outer join produit p on ac.Produit_ID = p.produit_id
left outer join produit_stock ps on ps.produit_id= p.produit_id
where  actt.type_tarif_id = @pPriceId  and ac.masquer = 0


select * from Adhesion_Catalog_Type_Tarif actt 
inner join Adhesion_Catalog ac on ac.Adhesion_Catalog_id = actt.Adhesion_Catalog_id
left outer join produit p on ac.Produit_ID = p.produit_id
where  actt.type_tarif_id = @pPriceId  and ac.masquer = 0

*/