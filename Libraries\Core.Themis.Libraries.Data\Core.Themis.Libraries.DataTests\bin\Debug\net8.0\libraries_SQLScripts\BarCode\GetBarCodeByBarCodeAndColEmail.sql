 /* 
 
declare @pColEmail int = 6
declare @pEventId int = 123

declare @pbarCode varchar(100)

set @pbarCode='2100054083'                                                                  
set @pbarCode='2100004092'   
set @pbarCode='4604300736'

 */


DECLARE @EventId int

if (@pEventId = 0)
begin
	SELECT top 1 @EventId = manifestation_id  from recette WHERE (externe = @pbarCode and motif='') or (motif = @pbarCode and externe='')
end
else
begin
	set @EventId = @pEventId
end

 
 DECLARE @sql nvarchar(max) = 'SELECT top 1	cl.commande_id as orderId,
rtrim(ltrim(case when externe <> '''' then externe else motif end)) as BarCode,
r.recette_id as RecetteId,
(select COUNT(*) from compte_client cc2 where cc2.cc_numpaiement = cc.cc_numpaiement  and cc2.mode_paiement_id > 0 and cc2.cc_credit >0
) as PaymentCB,
(SELECT MAX(recette_id) FROM recette rhisto WHERE rhisto.entree_id = r.entree_id AND rhisto.seance_id = r.seance_id) as LastRecetteId, -- derniere recette de ce billet
r.manifestation_id as EventId, 
r.seance_id as SessionId, 
s.seance_date_deb as SessionStartDate,
s.seance_date_fin as SessionEndDate,
ca.datecontrole as ControlledDate,
ca.Id as Controlled, 
r.numbillet as NumBillet, 
e.entree_id as SeatId,
rlp.rang as Rank,
rlp.siege as Seat,
z.zone_nom as ZoneName,
et.etage_nom as FloorName,
sect.Section_nom as SectionName, 

rlp.denomination_id as denominationId,
denom.denom_nom as denominationName,
rlp.tribune as tribuneId,
pptribune.nom as tribunename,
rlp.porte as gateId,
ppporte.nom as GateName,
rlp.acces as accessId,
ppacces.nom as accessName,
 
type_operation as TypeOperation, 
r.dossier_id as DossierId,

r.type_tarif_id as PriceId, 
tt.type_tarif_nom as PriceName,

r.categorie_id as CategId, 
cat.categ_nom as CategoryName,

ipay.identite_id as IdentiteId,
ipay.identite_prenom as FirstName,
ipay.identite_nom as SurName,
case @pColEmail 
when 1 then ipay.postal_tel1 
when 2 then ipay.postal_tel2
when 3 then ipay.postal_tel3
when 4 then ipay.postal_tel4
when 5 then ipay.postal_tel5
when 6 then ipay.postal_tel6
when 7 then ipay.postal_tel7

end as Email,

idos.identite_id as IdentiteId,
idos.identite_nom as FirstName,
idos.identite_prenom as SurName,
 
l.lieu_id as PlaceId,
l.lieu_nom as PlaceName,
l.lieu_code as PlaceCode,
l.lieu_rue1 as Address_address1,
l.lieu_rue2 as Address_address2,
l.lieu_rue3 as Address_address3,
l.lieu_rue4 as Address_address4,
l.lieu_cp as Address_postal_code,
l.lieu_ville as Address_city,

l.lieu_pays as Address_country,
l.lieu_region as Address_region,

prod.producteur_id as producteurId,
prod.producteur_nom as producteurNom,
prod.producteur_code as producteurCode,
prod.num_licence1 as producteurLicence


 
 --into #result

FROM recette  r
INNER JOIN commande_ligne cl on r.dossier_id = cl.dossier_id and r.seance_id = cl.seance_id and cl.type_ligne=''DOS''
INNER JOIN seance s on s.seance_id = cl.seance_id
INNER JOIN manifestation man ON man.manifestation_id = s.manifestation_id
LEFT JOIN producteur prod on prod.producteur_id = man.producteur_id
INNER JOIN lieu l on l.lieu_id = s.lieu_id
 
INNER JOIN type_tarif tt on tt.type_tarif_id = r.type_tarif_id
INNER JOIN categorie cat on cat.categ_id = r.categorie_id

LEFT JOIN entree_' + convert(varchar(10), @EventId) +  ' e on e.entree_id = r.entree_id and e.dossier_id = r.dossier_id and e.seance_id = r.seance_id
LEFT JOIN reference_lieu_physique rlp on rlp.iindex = e.iindex and e.reference_unique_physique_id = rlp.ref_uniq_phy_id

LEFT JOIN zone z on z.zone_id = rlp.zone_id
LEFT JOIN etage et on et.etage_id = rlp.etage_id
LEFT JOIN section sect on sect.section_id = rlp.section_id
LEFT JOIN denomination denom on rlp.denomination_id = denom.denom_id

LEFT JOIN Propriete_physique pptribune ON pptribune.id = rlp.tribune and pptribune.lieu_id = rlp.lieu_id and pptribune.codetable=''TRIBUNE''
LEFT JOIN Propriete_physique ppacces ON ppacces.id = rlp.acces and ppacces.lieu_id = rlp.lieu_id and ppacces.codetable=''ACCES''
LEFT JOIN Propriete_physique ppporte ON ppporte.id = rlp.porte and ppporte.lieu_id = rlp.lieu_id and ppporte.codetable=''PORTE''

INNER JOIN compte_client cc on cc.commande_id = cl.commande_id and cc.dossier_id = cl.dossier_id and cl.seance_id = cc.seance_id and cc.cc_intitule_operation = ''CREDITDOS''
INNER JOIN identite idos on idos.identite_id = cc.identite_id
/* le payeur cb, s''il existe : */
LEFT JOIN compte_client cc2 on cc2.cc_numpaiement = cc.cc_numpaiement and cc2.mode_paiement_id > 0 and cc2.cc_credit >0
LEFT JOIN identite ipay on ipay.identite_id = cc2.identite_id

LEFT OUTER JOIN Controle_Acces ca on ca.recette_id=r.recette_id and ca.statutretour = 1
WHERE (externe = @pbarCode and motif='''') or (motif = @pbarCode and externe='''')
--where idos.identite_id <> ipay.identite_id
ORDER BY r.recette_id desc'

print @sql
EXEC sp_executesql @sql, N'@pbarCode varchar(50), @pColEmail int', @pbarCode=@pbarCode, @pColEmail = @pColEmail


