﻿

--DECLARE @peventId int = 2
--DECLARE @psessionId int = 0

DECLARE @myEvent INT = 0

DECLARE @myManifs TABLE (manifestation_id int)
IF (@peventId = 0)
BEGIN
	IF (@psessionId > 0) -- manif = 0, session > 0
	BEGIN
		INSERT INTO @myManifs 
		SELECT DISTINCT manifestation_id FROM seance s WHERE s.seance_Id = @psessionId
	END
	ELSE
	BEGIN
		INSERT INTO @myManifs  -- manif = 0, session = 0 = tout
		SELECT DISTINCT manifestation_id FROM manifestation
	END
	
END
ELSE
BEGIN
	INSERT INTO @myManifs VALUES (@peventId) -- manif > 0
END

CREATE TABLE #mySessions (session_id int)
IF (@psessionId = 0)
BEGIN
	INSERT INTO #mySessions 
	SELECT DISTINCT seance_id  FROM seance
		INNER JOIN @myManifs m ON m.manifestation_id = seance.manifestation_id
		
END
ELSE
BEGIN
	INSERT INTO #mySessions 
	SELECT DISTINCT seance_id FROM seance
		INNER JOIN @myManifs m ON m.manifestation_id = seance.manifestation_id
		WHERE seance.seance_Id = @psessionId
END


CREATE TABLE #myResult (dossState varchar(2)
, eventId int, eventName varchar(50),sessionid int,
sessionStartDate datetime,  priceId int, priceName varchar(50), 
 categoryId int, categoryName varchar(50), seatsCount int, unitTTCAmount int,
SumTTCAmount int)

DECLARE db_cursor CURSOR FOR

SELECT DISTINCT manifestation_id
FROM @myManifs
GROUP BY manifestation_id

OPEN db_cursor   
FETCH NEXT FROM db_cursor INTO @myEvent

WHILE @@FETCH_STATUS = 0   
BEGIN 

DECLARE @sql varchar(5000)
   SET @sql = '
    --DECLARE @datedeb varchar(18); set @datedeb='''';
	--DECLARE @datefin varchar(18);set @datefin='''';

    SELECT ltrim(rtrim(dossier_etat)) as dossState, '+ convert(varchar,@myEvent)+' as eventid, 
	manifestation_nom as eventname, e.seance_id as sessionid, seance_date_deb as sessionStartDate, e.type_tarif_id as priceId, tt.type_tarif_nom as priceName, 
	e.categorie_id as categoryId,c.categ_nom as categoryName, COUNT(*) as seatsCount, e.montant1 * 100 as unitTTCAmount,
	COUNT(*) * e.montant1 * 100  as SumTTCAmount

    FROM dossier_' + convert(varchar,@myEvent) + ' d, entree_'+ convert(varchar,@myEvent)+' e , type_tarif tt,categorie c, manifestation m, seance s
	INNER JOIN #mySessions mys ON mys.session_id = s.seance_id
    WHERE e.categorie_id=c.categ_id
	AND e.dossier_id = d.dossier_id
	AND e.type_tarif_id=tt.type_tarif_id
	AND m.manifestation_id = ' + convert(varchar,@myEvent) + '
	AND e.seance_id = s.seance_id
	AND dossier_etat in  (''R'',''P'',''B'')
	GROUP BY e.type_tarif_id,  manifestation_nom, seance_date_deb, tt.type_tarif_nom, tt.pref_affichage, c.pref_affichage, e.categorie_id,c.categ_nom,e.seance_id , e.montant1, dossier_etat
	ORDER BY e.seance_id ,tt.pref_affichage, tt.type_tarif_nom, c.pref_affichage, categ_nom, montant1';

	print (@sql)
	 INSERT INTO #myResult 
		EXEC (@sql)

   FETCH NEXT FROM db_cursor INTO @myEvent
END
CLOSE db_cursor
DEALLOCATE db_cursor
DROP TABLE #mySessions

SELECT *FROM #myResult order by eventId, sessionId, categoryId, priceId

DROP TABLE #myResult