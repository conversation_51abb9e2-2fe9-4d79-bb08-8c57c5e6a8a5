﻿--drop table HomeModular_BlockUserConfig_Histo
--drop table BlockUserConfig_Version



-- check si la table HomeModular_BlockUserConfig existe
IF (EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES 
				WHERE   TABLE_NAME = 'HomeModular_BlockUserConfig')) 
BEGIN
					
	declare @nbRowsBlockUserConfig int = 0
	select @nbRowsBlockUserConfig = count(*) from HomeModular_BlockUserConfig

	if @nbRowsBlockUserConfig > 0
	BEGIN

	
		-- la table ConfigUser_Version n'existe pas 
		IF (NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES 
					 WHERE   TABLE_NAME = 'BlockUserConfig_Version')) 
		BEGIN

			-- créer la table ConfigUser_Version  
			create table BlockUserConfig_Version(buc_version int, date_operation datetime)
			INSERT INTO BlockUserConfig_Version VALUES (1, getdate())
	
	

			IF (NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES 
						 WHERE   TABLE_NAME = 'HomeModular_BlockUserConfig_Histo')) 
			BEGIN
		
				-- Sauvegarde des valeurs de BlockUserConfig dans BlockUserConfig_Histo
				select * into  HomeModular_BlockUserConfig_Histo from HomeModular_BlockUserConfig
		
		
				-- Sauvegarde des valeurs de HomeModular_BlockEmplacement dans HomeModular_BlockEmplacement_Histo
					IF (NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES 
								 WHERE   TABLE_NAME = 'HomeModular_BlockEmplacement_Histo')) 
					BEGIN
						select * into  HomeModular_BlockEmplacement_Histo from HomeModular_BlockEmplacement
					END

		
					Declare @TblBlockFunction table (BlockFunctionId int, BlockTypeId int, FunctionId int )

					INSERT INTO @TblBlockFunction VALUES (1,1,1)
					INSERT INTO @TblBlockFunction VALUES (2,2,1)
					INSERT INTO @TblBlockFunction VALUES (3,2,2)
					INSERT INTO @TblBlockFunction VALUES (4,3,1)
					INSERT INTO @TblBlockFunction VALUES (5,3,3)
					INSERT INTO @TblBlockFunction VALUES (29,10,17)
					INSERT INTO @TblBlockFunction VALUES (7,5,1)
					INSERT INTO @TblBlockFunction VALUES (8,5,4)
					INSERT INTO @TblBlockFunction VALUES (9,6,1)
					INSERT INTO @TblBlockFunction VALUES (10,7	,1 )
					INSERT INTO @TblBlockFunction VALUES (11,7	,5 )
					INSERT INTO @TblBlockFunction VALUES (12,8	,1 )
					INSERT INTO @TblBlockFunction VALUES (13,8	,6 )
					INSERT INTO @TblBlockFunction VALUES (14,9	,1 )
					INSERT INTO @TblBlockFunction VALUES (15,9	,7 )
					INSERT INTO @TblBlockFunction VALUES (16,10	,8 )
					INSERT INTO @TblBlockFunction VALUES (17,10	,9 )
					INSERT INTO @TblBlockFunction VALUES (18,10	,10)
					INSERT INTO @TblBlockFunction VALUES (19,10	,11)
					INSERT INTO @TblBlockFunction VALUES (20,10	,12)
					INSERT INTO @TblBlockFunction VALUES (21,10	,13)
					INSERT INTO @TblBlockFunction VALUES (30,10	,18)
					INSERT INTO @TblBlockFunction VALUES (23,12	,1 )
					INSERT INTO @TblBlockFunction VALUES (24,9	,14)
					INSERT INTO @TblBlockFunction VALUES (25,13	,1 )
					INSERT INTO @TblBlockFunction VALUES (26,13	,15)
					INSERT INTO @TblBlockFunction VALUES (27,10	,16)
					INSERT INTO @TblBlockFunction VALUES (28,14	,1 )
					INSERT INTO @TblBlockFunction VALUES (31,15	,17)
					INSERT INTO @TblBlockFunction VALUES (32,15	,18)
					INSERT INTO @TblBlockFunction VALUES (40,9	,24)
					INSERT INTO @TblBlockFunction VALUES (34,15	,20)
					INSERT INTO @TblBlockFunction VALUES (35,15	,21)
					INSERT INTO @TblBlockFunction VALUES (36,15	,19)
					INSERT INTO @TblBlockFunction VALUES (37,15	,22)
					INSERT INTO @TblBlockFunction VALUES (38,15	,23)
					INSERT INTO @TblBlockFunction VALUES (39,15	,16)



				DECLARE @blockUserConfigId INT,  @blockEmplacementId INT, @blockFunctionId INT
 
				DECLARE cursor_results CURSOR FOR
					select BlockUserConfig_ID, BlockEmplacement_ID, BlockFunction_ID from HomeModular_BlockUserConfig
				OPEN cursor_results


				FETCH NEXT FROM cursor_results into @blockUserConfigId,  @blockEmplacementId, @blockFunctionId
				WHILE @@FETCH_STATUS = 0
				BEGIN 
			
					declare @blockType int
					declare @functionId int
					declare @resultBlockFunctionId int

					--récupère le blockType de cet emplacement
					select @blockType = BlockType_ID from HomeModular_BlockEmplacement where BlockEmplacement_ID = @blockEmplacementId
					--récupère la function de cet emplacement
					select @functionId = BlockFunction_ID from HomeModular_BlockUserConfig where BlockEmplacement_ID = @blockEmplacementId
					--Récupère le blockFunction 
					select @resultBlockFunctionId = BlockFunctionId from @TblBlockFunction where BlockTypeId = @blockType and FunctionId = @blockFunctionId
			
					--select @resultBlockFunctionId as resultBlockFunctionId, *from HomeModular_BlockUserConfig where BlockUserConfig_ID = @blockUserConfigId
					
					if(@resultBlockFunctionId is not null)
					BEGIN
						update HomeModular_BlockUserConfig set BlockFunction_ID=@resultBlockFunctionId where BlockUserConfig_ID = @blockUserConfigId
					END


				FETCH NEXT FROM cursor_results into  @blockUserConfigId, @blockEmplacementId, @blockFunctionId
				END
 
				/* Clean up our work */
				CLOSE cursor_results;
				DEALLOCATE cursor_results;



				--select * from HomeModular_BlockUserConfig --where BlockEmplacement_ID=24
			END
		END



	END

END
