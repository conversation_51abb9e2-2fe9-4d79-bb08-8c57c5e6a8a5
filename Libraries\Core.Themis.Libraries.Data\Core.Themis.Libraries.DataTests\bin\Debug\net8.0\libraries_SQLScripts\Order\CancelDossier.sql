
UPDATE DOSSIER_[EVENTID] 
SET 
dossier_v=4, 
operateur_id=@poperatorId, 
dossier_c='', 
dossier_etat='A', 
dossier_icone=4097, 
dossier_montantpayer=0, 
dossier_facture=0, 
dossier_client_nom='', 
num_paiement='262861', 
date_operation=GETDATE(), 
operation_id=0
WHERE dossier_id = @pdossierid

INSERT INTO dossiersvg_[EVENTID] (dossier_id, identite_id, commande_id, abon_manif_id, seance_id, dossier_v, operateur_id, dossier_montant, dossier_c, dossier_etat, dossier_icone, dossier_numero, 
dossier_nbplace, dossier_montantpayer, dossier_facture, dossier_client_nom, num_paiement, date_operation, filiere_id, type_operation, dossier_montant1, dossier_montant2, dossier_montant3, 
dossier_montant4, dossier_montant5, dossier_montant6, dossier_montant7, dossier_montant8, dossier_montant9, dossier_montant10) 
SELECT dossier_id, identite_id, commande_id, abon_manif_id, 
seance_id, dossier_v, operateur_id, dossier_montant, dossier_c, dossier_etat, dossier_icone, dossier_numero, dossier_nbplace, dossier_montantpayer, dossier_facture, dossier_client_nom, 
num_paiement, date_operation, filiere_id, 'REMB', 
dossier_montant1, dossier_montant2, dossier_montant3, dossier_montant4, dossier_montant5, dossier_montant6, dossier_montant7, dossier_montant8, dossier_montant9, dossier_montant10  
FROM dossier_[EVENTID] WHERE dossier_id = @pdossierid

INSERT INTO histodossier (date_operation, operateur_id, manif_id, seance_id, dossier_id, etat, commande_id, dossier_v, type) 
SELECT date_operation, operateur_id, @pManifId, seance_id, dossier_id, dossier_etat, commande_id, dossier_v, 'DOS' FROM dossier_[EVENTID] WHERE dossier_id = @pdossierid;