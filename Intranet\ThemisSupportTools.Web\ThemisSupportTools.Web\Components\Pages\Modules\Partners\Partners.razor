@page "/partners"
@*@attribute [Authorize(Roles = "Administrator")]*@
@inject ILogger<Partners> Lo<PERSON>
@inject NavigationManager NavigationManager
@inject IPartnerManager PartenairesManager
@inject IConfiguration Configuration
@inject ITstAccessService TstAccessService
@inject IStringLocalizer<Resource> Localizer
@inject IJSRuntime JSRuntime
@using ThemisSupportTools.Web.Components.Pages.GlobalStyles
@using ThemisSupportTools.Web.Components.Shared

@inject IWsAdminStructuresManager WsAdminStructuresManager



<Core.Themis.Libraries.Razor.Common.Components.Dialog @ref="DeleteConfirmation" EventChanged="ConfirmDelete_Click" ButtonValider="Confirmer" ButtonCancel="Annuler"
                                                      Message=@($"Etes-vous sûr de vouloir supprimer ce partenaire <strong>{partnerDTOSelected.PartnerName}</strong>")>
</Core.Themis.Libraries.Razor.Common.Components.Dialog>

<BlazorBootstrap.Modal @ref="modal" Id="modal-partner"
                       UseStaticBackdrop="false"
                       CloseOnEscape="false"
                       Size="ModalSize.Large">

    <HeaderTemplate>
        <div class="d-flex justify-content-center">
            @if (isEditMode)
            {
                <h5>@Localizer["edit_partenaire"]</h5>
            }
            else
            {
                <h5>@Localizer["new_partner"]</h5>
            }
        </div>
    </HeaderTemplate>

    <BodyTemplate>

        <PartnerForm OnPartnerSubmitted="OnPartnerSubmitted"
                     PartnerDTO="PartnerDTO "
                     OnCancel="OnCancelForm"
                     isEditingMode="isEditMode"
                     ModalId="#modal-partner" />


    </BodyTemplate>
</BlazorBootstrap.Modal>
<ConfirmDialog @ref="dialog" />

<Toasts class="p-3" Messages="toastMessages" Delay="6000" Placement="@toastsPlacement" />



<div class="d-flex justify-content-center mb-3">
    <button class="btn btn-primary" @onclick="ShowCreateForm">
        @Localizer["new_partner"]
    </button>
</div>


<h3 style="text-align: center">@Localizer["choose_partner"]</h3>



@if (isLoading)
{
    <div class="spinner-border text-info" role="status">
        <span class="visually-hidden"></span>
    </div>
}
else
{

    <SearchBox OnSearch="@OnSearchAsync"></SearchBox>

    <div class="col-12">
        <div class="list-group">
            @foreach (var ptr in partenaires)
            {
                <DynamicStyle SelectedId="currentlySelectedPartnerId"
                              class="list-group-item list-group-item-action partner-item d-flex gap-3 py-3" aria-current="true"
                              Id="@ptr.PartnerId">

                    <div id="@ptr.PartnerId" class="w-100">
                        <!-- En-tête avec nom et rôles -->
                        <div class="d-flex align-items-start justify-content-between mb-3">
                            <div class="flex-grow-1">
                                <h6 class="mb-2 partner-name">
                                    <i class="fas fa-handshake me-2 text-primary"></i>
                                    @ptr.PartnerName
                                </h6>

                            </div>

                            <!-- Actions -->
                            <div class="d-flex gap-2 partner-actions">
                                <button class="btn btn-outline-warning btn-sm" @onclick="() => EditPartner(ptr)">
                                    <i class="fas fa-edit me-1"></i>
                                    @Localizer["button_edit"]
                                </button>
                                <button class="btn btn-outline-danger btn-sm" @onclick="() => DeletePartner(ptr)" @onclick:preventDefault>
                                    <i class="fas fa-trash me-1"></i>
                                    @Localizer["button_delete"]
                                </button>
                            </div>
                        </div>

                        <!-- Section structures -->
                        <div class="structures-section">
                            <div class="d-flex align-items-center">
                                <span class="structures-label me-3" style="min-width: 90px;">
                                    <i class="fas fa-building me-1"></i>
                                    Structures:
                                </span>
                                @if (ptr.LstStructuresLinked?.Any() == true)
                                {
                                    <div class="flex-grow-1">
                                        @foreach (var structure in ptr.LstStructuresLinked.Take(4))
                                        {
                                            <span class="badge bg-primary me-1">@structure.StructureId</span>
                                        }
                                        @if (ptr.LstStructuresLinked.Count > 4)
                                        {
                                            <span class="text-dark">+@(ptr.LstStructuresLinked.Count - 4)</span>
                                        }
                                    </div>
                                }
                                else
                                {
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        @Localizer["no_partner_structures"]
                                    </span>
                                }
                            </div>
                        </div>
                    </div>
                </DynamicStyle>

            }
        </div>
    </div>



}

<!-- Section de sélection de structure - placée après la liste des partenaires -->
<div class="card mt-4 border-primary">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">@Localizer["choose_structure"]</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <label class="form-label">@Localizer["select_structure"]</label>
                <select class="form-select" @onchange="OnStructureSelected">
                    <option value="">-- @Localizer["select_structure_placeholder"] --</option>
                    @foreach (var structure in StructuresDTO)
                    {
                        <option value="@structure.StructureId">@structure.StructureName (ID: @structure.StructureId)</option>
                    }
                </select>
            </div>
        </div>

        @if (selectedStructureForFilter == null)
        {
            <div class="text-center py-4">
                <p class="text-muted">@Localizer["select_structure_to_view_partners"]</p>
            </div>
        }
    </div>
</div>




