﻿@page "/partners"
@*@attribute [Authorize(Roles = "Administrator")]*@
@inject ILogger<Partners> Lo<PERSON>
@inject NavigationManager NavigationManager
@inject IPartnerManager PartenairesManager
@inject IConfiguration Configuration
@inject ITstAccessService TstAccessService
@inject IStringLocalizer<Resource> Localizer
@inject IJSRuntime JSRuntime
@using ThemisSupportTools.Web.Components.Pages.GlobalStyles
@using ThemisSupportTools.Web.Components.Shared

@inject IWsAdminStructuresManager WsAdminStructuresManager



<Core.Themis.Libraries.Razor.Common.Components.Dialog @ref="DeleteConfirmation" EventChanged="ConfirmDelete_Click" ButtonValider="Confirmer" ButtonCancel="Annuler"
                                                      Message=@($"Etes-vous sûr de vouloir supprimer ce partenaire <strong>{partnerDTOSelected.PartnerName}</strong>")>
</Core.Themis.Libraries.Razor.Common.Components.Dialog>

<BlazorBootstrap.Modal @ref="modal" Id="modal-partner"
                       UseStaticBackdrop="false"
                       CloseOnEscape="false"
                       Size="ModalSize.Large">

    <HeaderTemplate>
        <div class="d-flex justify-content-center">
            @if (isEditMode)
            {
                <h5>@Localizer["edit_partenaire"]</h5>
            }
            else
            {
                <h5>@Localizer["new_partner"]</h5>
            }
        </div>
    </HeaderTemplate>

    <BodyTemplate>

        <PartnerForm OnPartnerSubmitted="OnPartnerSubmitted"
                     PartnerDTO="PartnerDTO "
                     OnCancel="OnCancelForm"
                     isEditingMode="isEditMode"
                     ModalId="#modal-partner" />


    </BodyTemplate>
</BlazorBootstrap.Modal>
<ConfirmDialog @ref="dialog" />

<Toasts class="p-3" Messages="toastMessages" Delay="6000" Placement="@toastsPlacement" />

<!-- Section Choisir une structure -->
<div class="card mb-4 border-primary">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">@Localizer["choose_structure"]</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <label class="form-label">@Localizer["select_structure"]</label>
                <select class="form-select" @onchange="OnStructureSelected">
                    <option value="">-- @Localizer["select_structure_placeholder"] --</option>
                    @foreach (var structure in StructuresDTO)
                    {
                        <option value="@structure.StructureId">@structure.StructureName (ID: @structure.StructureId)</option>
                    }
                </select>
            </div>

            <!-- Champ de recherche (visible seulement si une structure est sélectionnée) -->
            @if (selectedStructureForFilter != null)
            {
                <div class="col-md-6">
                    <label class="form-label">@Localizer["search_partners_in_structure"]</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text"
                               class="form-control"
                               placeholder="@Localizer["search_partner_placeholder"]"
                               @bind="structurePartnerSearchTerm"
                               @bind:event="oninput"
                               @onkeyup="@(() => FilterPartnersByStructure())" />
                        @if (!string.IsNullOrEmpty(structurePartnerSearchTerm))
                        {
                            <button class="btn btn-outline-secondary" @onclick="ClearSearch">
                                <i class="fas fa-times"></i>
                            </button>
                        }
                    </div>
                </div>
            }
        </div>

        @if (selectedStructureForFilter == null)
        {
            <div class="text-center py-4">
                <p class="text-muted">@Localizer["select_structure_to_view_partners"]</p>
            </div>
        }
        else
        {
            <hr class="my-3" />
            <div class="structure-partners-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">
                        <i class="fas fa-handshake me-2 text-success"></i>
                        @Localizer["partners_of_structure"] @selectedStructureForFilter.StructureName
                    </h6>
                    <div class="badge bg-info">
                        @structureFilteredPartners.Count @Localizer["partners_found"]
                    </div>
                </div>

                @if (structureFilteredPartners.Any())
                {
                    <div class="row">
                        @foreach (var partner in structureFilteredPartners)
                        {
                            <div class="col-lg-6 col-xl-4 mb-3">
                                <div class="card border-success h-100">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            @partner.PartnerName
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="text-center py-4">
                        <i class="fas fa-info-circle fa-2x text-muted mb-2"></i>
                        <p class="text-muted">@Localizer["no_partners_in_structure"]</p>
                    </div>
                }
            </div>
        }
    </div>
</div>

<div class="d-flex justify-content-center mb-3">
    <button class="btn btn-primary" @onclick="ShowCreateForm">
        @Localizer["new_partner"]
    </button>
</div>


<h3 style="text-align: center">@Localizer["choose_partner"]</h3>



@if (isLoading)
{
    <div class="spinner-border text-info" role="status">
        <span class="visually-hidden"></span>
    </div>
}
else
{

    <SearchBox OnSearch="@OnSearchAsync"></SearchBox>

    <div class="col-12">
        <div class="list-group">
            @foreach (var ptr in partenaires)
            {
                <DynamicStyle SelectedId="currentlySelectedPartnerId"
                              class="list-group-item list-group-item-action partner-item d-flex gap-3 py-3" aria-current="true"
                              Id="@ptr.PartnerId">

                    <div id="@ptr.PartnerId" class="w-100">
                        <!-- En-tête avec nom et rôles -->
                        <div class="d-flex align-items-start justify-content-between mb-3">
                            <div class="flex-grow-1">
                                <h6 class="mb-2 partner-name">
                                    <i class="fas fa-handshake me-2 text-primary"></i>
                                    @ptr.PartnerName
                                </h6>

                            </div>

                            <!-- Actions -->
                            <div class="d-flex gap-2 partner-actions">
                                <button class="btn btn-outline-warning btn-sm" @onclick="() => EditPartner(ptr)">
                                    <i class="fas fa-edit me-1"></i>
                                    @Localizer["button_edit"]
                                </button>
                                <button class="btn btn-outline-danger btn-sm" @onclick="() => DeletePartner(ptr)" @onclick:preventDefault>
                                    <i class="fas fa-trash me-1"></i>
                                    @Localizer["button_delete"]
                                </button>
                            </div>
                        </div>

                        <!-- Section rôles et structures -->
                        <div class="d-flex align-items-center justify-content-between">
                            <!-- Rôles à gauche -->
                            <div>
                                @if (ptr.LstRolesOfPartnerForSelect2?.Any() == true)
                                {
                                    <span class="badge" style="background-color: #6c757d; color: white;">
                                        Rôles: @string.Join(", ", ptr.LstRolesOfPartnerForSelect2)
                                    </span>
                                }
                            </div>

                            <!-- Structures et boutons à droite -->
                            <div class="d-flex align-items-center gap-2">
                                @if (ptr.LstStructuresLinked?.Any() == true)
                                {
                                    <span class="badge" style="background-color: #17a2b8; color: white;">
                                        Structures: @ptr.LstStructuresLinked.Count
                                    </span>
                                }
                                else
                                {
                                    <span class="badge" style="background-color: #17a2b8; color: white;">
                                        Structures: 0
                                    </span>
                                }

                                <!-- Boutons d'action -->
                                <button class="btn btn-outline-warning btn-sm" @onclick="() => EditPartner(ptr)">
                                    <i class="fas fa-edit me-1"></i>
                                    @Localizer["button_edit"]
                                </button>
                                <button class="btn btn-outline-danger btn-sm" @onclick="() => DeletePartner(ptr)" @onclick:preventDefault>
                                    <i class="fas fa-trash me-1"></i>
                                    @Localizer["button_delete"]
                                </button>
                            </div>
                        </div>
                    </div>
                </DynamicStyle>

            }
        </div>
    </div>



}




