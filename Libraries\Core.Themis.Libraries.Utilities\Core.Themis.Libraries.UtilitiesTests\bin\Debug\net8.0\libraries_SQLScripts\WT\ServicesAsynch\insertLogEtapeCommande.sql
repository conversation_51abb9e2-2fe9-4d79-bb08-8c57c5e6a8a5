﻿


DECLARE @etpId int 

IF EXISTS(SELECT 1 FROM sys.columns 
          WHERE Name = N'action_id'
          AND Object_ID = Object_ID(N'logs_etapes_creationCmds'))
BEGIN
	SELECT @etpId = etape_id FROM logs_etapes_creationCmds_reference r WHERE r.code=@etat
	INSERT INTO logs_etapes_creationCmds (panier_id, action_id, etape_id, etat, comment) VALUES (@basketId,@actionid, @etpId, @boolEtapeDF, @comment)
END
ELSE
BEGIN
	SELECT @etpId = etape_id FROM logs_etapes_creationCmds_reference r WHERE r.code=@etat
	INSERT INTO logs_etapes_creationCmds (panier_id, etape_id, etat, comment) VALUES (@basketId,@etpId,@boolEtapeDF, @comment)
END