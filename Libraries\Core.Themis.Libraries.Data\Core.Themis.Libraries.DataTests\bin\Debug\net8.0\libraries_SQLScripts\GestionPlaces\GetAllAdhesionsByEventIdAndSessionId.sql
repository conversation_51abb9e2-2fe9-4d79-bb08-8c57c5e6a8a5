﻿/*
DECLARE @pEventId int = 69
DECLARE @pSessionId int = 2409
*/



SELECT * 
FROM gestion_place gp
	INNER JOIN seance s ON gp.seance_id = s.seance_Id
	INNER JOIN manifestation m ON gp.manif_id = m.manifestation_id
	INNER JOIN offre_gestion_place ogp ON gp.gestion_place_id = ogp.gestion_place_id
	INNER JOIN adhesion_catalog_offresliees o ON ogp.offre_id = o.offre_id
	LEFT JOIN offre_contrainte oc ON oc.offre_id = o.offre_id
WHERE 
	s.seance_cloturer = 'N' 
	AND s.seance_verrouiller = 'N' 
	AND s.supprimer = 'N' 
	AND m.supprimer = 'N'
	AND isContrainteIdentite = 1
	AND oc.contrainte_id IS NULL /* offres adh sans contraintes */
	AND formule_id IS NULL
	AND gp.isvalide = 1 AND gp.manif_id = @pEventId AND gp.seance_id = @pSessionId

