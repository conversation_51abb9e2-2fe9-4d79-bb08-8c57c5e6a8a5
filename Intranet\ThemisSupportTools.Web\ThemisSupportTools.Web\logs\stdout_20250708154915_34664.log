info: Microsoft.Hosting.Lifetime[0]
      Application started. Press Ctrl+C to shut down.
info: Microsoft.Hosting.Lifetime[0]
      Hosting environment: local
info: Microsoft.Hosting.Lifetime[0]
      Content root path: D:\WORK\TST_LD\ThemisSupportTools_LD\Intranet\ThemisSupportTools.Web\ThemisSupportTools.Web
fail: Microsoft.AspNetCore.Diagnostics.ExceptionHandlerMiddleware[1]
      An unhandled exception has occurred while executing the request.
      System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
         at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
         at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
         at System.Data.SqlClient.SqlConnection.Open()
         at Core.Themis.Libraries.Data.Repositories.Common.GenericRepository`1.GetAll() in D:\WORK\TST_LD\ThemisSupportTools_LD\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\Repositories\Common\GenericRepository.cs:line 65
         at Core.Themis.Libraries.Data.Repositories.Common.GenericRepository`1.GetAllCustomEntities() in D:\WORK\TST_LD\ThemisSupportTools_LD\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\Repositories\Common\GenericRepository.cs:line 723
         at Core.Themis.Libraries.BLL.PartnerManager.GetAllPartners() in D:\WORK\TST_LD\ThemisSupportTools_LD\Libraries\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLL\PartnerManager.cs:line 931
         at ThemisSupportTools.Web.Components.Pages.Modules.Partners.Partners.LoadPartners() in D:\WORK\TST_LD\ThemisSupportTools_LD\Intranet\ThemisSupportTools.Web\ThemisSupportTools.Web\Components\Pages\Modules\Partners\Partners.razor.cs:line 151
         at ThemisSupportTools.Web.Components.Pages.Modules.Partners.Partners.OnInitialized() in D:\WORK\TST_LD\ThemisSupportTools_LD\Intranet\ThemisSupportTools.Web\ThemisSupportTools.Web\Components\Pages\Modules\Partners\Partners.razor.cs:line 40
         at Microsoft.AspNetCore.Components.ComponentBase.RunInitAndSetParametersAsync()
         at Microsoft.AspNetCore.Components.Rendering.ComponentState.SupplyCombinedParameters(ParameterView directAndCascadingParameters)
         at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewSubtree(DiffContext& diffContext, Int32 frameIndex)
         at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InsertNewFrame(DiffContext& diffContext, Int32 newFrameIndex)
         at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.AppendDiffEntriesForRange(DiffContext& diffContext, Int32 oldStartIndex, Int32 oldEndIndexExcl, Int32 newStartIndex, Int32 newEndIndexExcl)
         at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
         at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
      --- End of stack trace from previous location ---
         at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
         at Microsoft.AspNetCore.Components.ComponentBase.StateHasChanged()
         at Microsoft.AspNetCore.Components.ComponentBase.RunInitAndSetParametersAsync()
         at Microsoft.AspNetCore.Components.Rendering.ComponentState.SupplyCombinedParameters(ParameterView directAndCascadingParameters)
         at Microsoft.AspNetCore.Components.Rendering.ComponentState.SetDirectParameters(ParameterView parameters)
         at Microsoft.AspNetCore.Components.RenderTree.Renderer.RenderRootComponentAsync(Int32 componentId, ParameterView initialParameters)
         at Mi