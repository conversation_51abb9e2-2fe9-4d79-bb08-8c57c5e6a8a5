
INSERT INTO entreesvg_[EVENTID] (entree_id,seance_id,dossier_id,categorie_id,type_tarif_id,numero_billet,alotissement_id,reserve_id,contingent_id, 
montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,entree_etat,dateoperation,
dossier_v,valeur_tarif_stock_id,valeurtarifstockversion)
(SELECT entree_id,seance_id,dossier_id,categorie_id,type_tarif_id,numero_billet,alotissement_id,reserve_id,contingent_id, montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,'L',
getdate(), 4, @poperatorId, 0 
FROM entree_[EVENTID]  WHERE dossier_id = @pDossierId AND seance_id = @pSeanceId AND ENTREE_ETAT ='R')

INSERT INTO entreesvg_[EVENTID] (entree_id,seance_id,dossier_id,categorie_id,type_tarif_id,numero_billet,alotissement_id,reserve_id,contingent_id,
montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,entree_etat,dateoperation,
dossier_v,valeur_tarif_stock_id,valeurtarifstockversion)
(SELECT entree_id,seance_id,dossier_id,categorie_id,type_tarif_id,numero_billet,alotissement_id,reserve_id,contingent_id, montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,'L',
getdate(), 4, @poperatorId, 1 
FROM entree_[EVENTID]  WHERE dossier_id = @pDossierId AND seance_id = @pSeanceId AND ENTREE_ETAT ='P')

INSERT INTO entreesvg_[EVENTID] (entree_id,seance_id,dossier_id,categorie_id,type_tarif_id,numero_billet,alotissement_id,reserve_id,contingent_id, 
montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,entree_etat,dateoperation,
dossier_v,valeur_tarif_stock_id,valeurtarifstockversion)
(SELECT entree_id,seance_id,dossier_id,categorie_id,type_tarif_id,numero_billet,alotissement_id,
reserve_id,contingent_id, montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,'L',
getdate(), 4, @poperatorId, 2  
FROM entree_[EVENTID]  WHERE dossier_id = @pDossierId AND seance_id = @pSeanceId AND entree_etat ='B')

UPDATE entree_[EVENTID] SET 
entree_etat = 'L', 
dossier_id = 0, 
type_tarif_id = 0, 
valeur_tarif_stock_id = 0, 
flag_selection = '',
montant1 = 0, 
montant2 = 0, 
montant3 = 0, 
montant4 = 0, 
montant5 = 0, 
montant6 = 0, 
montant7 = 0, 
montant8 = 0, 
montant9 = 0, 
montant10 = 0, 
dateoperation = getdate(), 
controleacces = null 
WHERE DOSSIER_ID = @pDossierId AND SEANCE_ID = @pSeanceId
