﻿

/*
declare @pSessionId int = 8873;
declare @pInternetUserName varchar(500) = 'VODISLAV'
declare @pInternetUserFirstname varchar(500) = 'Elisa';
declare @pReferenceDossier varchar(50) = '25184637';
*/

SELECT distinct d.dossier_id, i.identite_id, c.commande_id, dossier_etat, identite_nom,
identite_prenom, d.operateur_id, d.filiere_id,
case   when  r.numbillet IS NULL then 0 else 1 end as iscommandeditee, e.entree_id
FROM dossier_[EVENTID] d
inner join commande c on d.commande_id = c.commande_id
	inner join identite i on i.identite_id = c.identite_id
	inner join entree_[EVENTID] e on e.dossier_id = d.dossier_id 
	inner join reference_lieu_physique rlp on rlp.ref_uniq_phy_id = e.reference_unique_physique_id

	  left outer join recette r on r.dossier_id=d.dossier_id and r.seance_id=d.seance_id and r.entree_id = e.entree_id
         left outer join Controle_Acces ca on ca.recette_id=r.recette_id
		  inner join commande_infos ci on ci.commande_id = c.commande_id
WHERE FicheSupprimer='N' 
 --AND LTRIM(RTRIM(d.dossier_client_nom))  = LTRIM(RTRIM(@pInternetUserName)) + ' ' + LTRIM(RTRIM(@pInternetUserFirstname))
 AND (UPPER(REPLACE(LTRIM(RTRIM(d.dossier_client_nom)), ' ', ''))  = UPPER(REPLACE(LTRIM(RTRIM(@pInternetUserName collate SQL_Latin1_General_CP1251_CS_AS)) + LTRIM(RTRIM(@pInternetUserFirstname collate SQL_Latin1_General_CP1251_CS_AS)), ' ', '')) OR UPPER(REPLACE(LTRIM(RTRIM(d.dossier_client_nom)), ' ', ''))  = UPPER(REPLACE(LTRIM(RTRIM(@pInternetUserFirstname collate SQL_Latin1_General_CP1251_CS_AS)) + LTRIM(RTRIM(@pInternetUserName collate SQL_Latin1_General_CP1251_CS_AS)), ' ', ''))) 
AND d.dossier_etat in ('P', 'B')
and ca.ID is null 
AND d.seance_id = @pSessionId
AND  LTRIM(RTRIM(ci.commentaire)) like 'idtrsct%|'+@pReferenceDossier






/*





SELECT distinct d.dossier_id, i.identite_id, c.commande_id, dossier_etat, identite_nom, identite_prenom, d.operateur_id, d.filiere_id,
case   when  r.numbillet IS NULL then 0 else 1 end as iscommandeditee
FROM dossier_[EVENTID] d
inner join commande c on d.commande_id = c.commande_id
	inner join identite i on i.identite_id = c.identite_id
	inner join entree_[EVENTID] e on e.dossier_id = d.dossier_id 
	inner join reference_lieu_physique rlp on rlp.ref_uniq_phy_id = e.reference_unique_physique_id

	  left outer join recette r on r.dossier_id=d.dossier_id and r.seance_id=d.seance_id and r.entree_id = e.entree_id
         left outer join Controle_Acces ca on ca.recette_id=r.recette_id
		  inner join commande_infos ci on ci.commande_id = c.commande_id
WHERE FicheSupprimer='N' 
--AND identite_nom = '[IDENTITE_NOM]' AND identite_prenom = '[IDENTITE_PRENOM]'
 AND LTRIM(RTRIM(d.dossier_client_nom))  = LTRIM(RTRIM(@pInternetUserName)) + ' ' + LTRIM(RTRIM(@pInternetUserFirstname))
AND d.dossier_etat in ('P', 'B')
--and ca.ID is null //ne pas oublier de remettre
AND d.seance_id = @pSessionId
AND ci.commentaire like 'idtrsct%|'+@pReferenceDossier


_____________________________________________

SELECT distinct d.dossier_id, i.identite_id, c.commande_id, dossier_etat, identite_nom, identite_prenom, d.operateur_id, d.filiere_id,
case   when  r.numbillet IS NULL then 0 else 1 end as iscommandeditee
FROM dossier_[EVENTID] d
inner join commande c on d.commande_id = c.commande_id
	inner join identite i on i.identite_id = c.identite_id
	inner join entree_[EVENTID] e on e.dossier_id = d.dossier_id 
	inner join reference_lieu_physique rlp on rlp.ref_uniq_phy_id = e.reference_unique_physique_id

	  left outer join recette r on r.dossier_id=d.dossier_id and r.seance_id=d.seance_id and r.entree_id = e.entree_id
         left outer join Controle_Acces ca on ca.recette_id=r.recette_id
		  inner join commande_infos ci on ci.commande_id = c.commande_id
WHERE FicheSupprimer='N' 
--AND identite_nom = '[IDENTITE_NOM]' AND identite_prenom = '[IDENTITE_PRENOM]'
 AND LTRIM(RTRIM(d.dossier_client_nom))  = LTRIM(RTRIM('[IDENTITE_NOM]')) + ' ' + LTRIM(RTRIM('[IDENTITE_PRENOM]'))
AND d.dossier_etat in ('P', 'B')
--and ca.ID is null //ne pas oublier de remettre
AND d.seance_id = [SESSION_ID]
AND ci.commentaire like 'idtrsct%|[REFERENCE_DOSSIER]'
*/