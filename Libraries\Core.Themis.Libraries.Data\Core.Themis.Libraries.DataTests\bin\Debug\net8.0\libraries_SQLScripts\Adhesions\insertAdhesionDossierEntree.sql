﻿INSERT INTO adhesion_dossier_entree (adhesion_adherent_catalog_id, entree_id, manifestation_id, seance_id, commande_id, dossier_id, etat, type_tarif_id, 
entree_complement_id, date_operation, operateur_id, filiere_id, version, internet)
select @padhesion_id, entree_id, @pmanif_id, seance_id, @pcommande_id, dossier_id, entree_etat, type_tarif_id, 0, getdate(), @poperatorid, @pfiliere_id, 1,1
from entree_[EVENTID] where entree_id = @pentree_id and dossier_id =@pdossier_id and type_tarif_id=@ptarif_id

insert into Adhesion_Dossier_Entree_SVG 
select * from adhesion_dossier_entree where commande_id = @pcommande_id and adhesion_adherent_catalog_id=@padhesion_id and entree_id=@pentree_id and manifestation_id=@pmanif_id
