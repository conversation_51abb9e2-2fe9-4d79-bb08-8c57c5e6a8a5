﻿/* UpdateTypeEnvoiAndMaquette.sql

declare @pBasketId int
set @pBasketId = 5650001

declare @pObtainingModeId int
set @pObtainingModeId = 5650001

declare @pMaquetteId int
set @pMaquetteId = 5650001

 */


UPDATE panier_entree 
    SET maquette_id=@pMaquetteId ,type_envoi_id= @pObtainingModeId,
    type_envoi = case when @pMaquetteId = 0 THEN null ELSE 'PRINT@HOME' END 
    WHERE panier_id=@pBasketId  AND gestion_place_id in ({gpids})


UPDATE panier_entree_abo 
    SET maquette_id=@pMaquetteId ,type_envoi_id= @pObtainingModeId,
    type_envoi = case when @pMaquetteId = 0 THEN null ELSE 'PRINT@HOME' END 
    WHERE panier_id=@pBasketId  AND gestion_place_id in ({gpids})
