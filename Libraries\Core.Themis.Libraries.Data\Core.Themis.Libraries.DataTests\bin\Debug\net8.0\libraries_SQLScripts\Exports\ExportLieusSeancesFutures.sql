﻿SELECT distinct l.lieu_id as place_id, l.lieu_nom as place_name

/* parcourt des lieus ayant une seance a venir (mais pas dans 10 ans) */
FROM [seance] s
inner join lieu l on l.lieu_id = s.lieu_id
WHERE seance_date_fin > getdate()
AND (seance_date_deb<dateadd(year, 10, getdate())
OR seance_date_deb > Convert(datetime, '2099-01-01' )) -- date libre
AND seance_verrouiller = 'N'
order by l.lieu_nom, l.lieu_id
