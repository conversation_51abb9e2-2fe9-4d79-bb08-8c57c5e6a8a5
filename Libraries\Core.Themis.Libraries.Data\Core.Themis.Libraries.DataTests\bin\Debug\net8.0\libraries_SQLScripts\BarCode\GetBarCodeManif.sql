 /* 
 
declare @pbarCode varchar(100)

set @pbarCode='2100054083'                                                                  
set @pbarCode='2100004092'   
set @pbarCode='4604300736'

 */

DECLARE @EventId int

SELECT top 1 @EventId = manifestation_id  from recette r WHERE ((externe = @pbarCode AND motif='') or (motif = @pbarCode and externe=''))
	--AND r.recette_id = (SELECT MAX(recette_id) FROM recette rhisto WHERE rhisto.entree_id = r.entree_id AND rhisto.seance_id = r.seance_id) -- check que c''est bien la derniere recette

SELECT @EventId

