﻿

DECLARE @countResult int = 0



DECLARE @statesPossible table (et varchar(3))
insert into @statesPossible values ('ALL')
insert into @statesPossible values ('P')
insert into @statesPossible values ('V')
insert into @statesPossible values ('C')
insert into @statesPossible values ('I')
insert into @statesPossible values ('E') -- erreur
insert into @statesPossible values ('PP')
insert into @statesPossible values ('VP')
insert into @statesPossible values ('R')
insert into @statesPossible values ('NS')

SELECT distinct et
INTO #etatCherche 
FROM @statesPossible where et in ({pstates})

DECLARE @ChercheNoEtat int 
SELECT @ChercheNoEtat = COUNT(*) from #etatCherche where et='NS'

DECLARE @ChercheErreur int 
SELECT @ChercheErreur = COUNT(*) from #etatCherche where et='E'

DECLARE @ChercheAllEtat int 
SELECT @ChercheAllEtat = COUNT(*) from #etatCherche where et='ALL'
IF (@ChercheAllEtat>0)
BEGIN
    insert into #etatCherche
    select et from @statesPossible
END



DECLARE @etatChercheExist int 
SELECT @etatChercheExist = COUNT(*) from #etatCherche

IF (@ChercheNoEtat > 0)
BEGIN

     SELECT @countResult = count(distinct user_id)

    FROM users 
    LEFT JOIN panier p ON p.web_user_id = users.user_id
    WHERE 
    users.start_date >= @pdate1
    and users.start_date < @pdate2

    [where_identite]
    [where_ip]
    [where_order]
    [where_basket]
    [where_email]
    [where_transaction]


END
ELSE
BEGIN

	IF (@ChercheErreur = 0)
    BEGIN

        SELECT @countResult = count(distinct web_user_id)

             FROM users 
	    INNER JOIN panier p on p.web_user_id = users.user_id
	    INNER JOIN #etatCherche etc on etc.et = p.etat

        WHERE 
            users.start_date >= @pdate1
            and users.start_date < @pdate2

            [where_identite]
            [where_ip]
            [where_order]
            [where_basket]
            [where_email]
            [where_transaction]

    END
    ELSE
    BEGIN -- chercher les paniers en erreurs

    
        SELECT @countResult = count(distinct web_user_id)

             FROM users 
	    INNER JOIN panier p on p.web_user_id = users.user_id
        INNER JOIN logs_etapes_creationCmds l on l.panier_id = p.panier_id AND l.etat=99

        WHERE 
            users.start_date >= @pdate1
            and users.start_date < @pdate2

            [where_identite]
            [where_ip]
            [where_order]
            [where_basket]
            [where_email]
            [where_transaction]

    END

    

END

DROP TABLE #etatCherche

select @countResult
