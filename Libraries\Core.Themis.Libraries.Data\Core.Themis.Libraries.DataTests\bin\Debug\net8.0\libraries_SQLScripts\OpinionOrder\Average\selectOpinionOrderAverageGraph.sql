﻿declare     
@START_NUMBER int,
@END_NUMBER int,
--paramètres locales
@datestart datetime,
@dateend datetime,
@intervalleMinute int,
@SQL varchar(max)

declare @TableTmp table (filiere_id int)

--parametres passés par le c#
/*
@pStructureid varchar(3),
@pIntervalleMinute int,
@pQuestionCode varchar(5),
@pStartDate varchar(20),
@pEndDate varchar(20),
@pLstSaleChannels varchar(max)


set @pStartDate = '04/05/2020'
set @pEndDate = '29/05/2020'

set @pLstSaleChannels = '9940003'
*/

--défini des valeurs par défaut si les dates ne sont pas renseignées
set @datestart = DATEADD(DAY, -7, getdate()) -- FORMAT(GETDATE()-7, 'dd/MM/yyyy') --
set @dateend = GETDATE()


set @intervalleMinute=1 -- 1 => jour par jour; 7 => 7 jour par 7 jour, etc
/*
if @pIntervalleMinute > 0
begin
	set @intervalleMinute = @pIntervalleMinute
end
*/


if @pLstSaleChannels <> ''
	begin
		set @SQL = 'select distinct filiere_id from OpinionOrderForms where filiere_id in ('+ @pLstSaleChannels +')'
		insert into @TableTmp exec(@SQL)
	end


if @pLstSaleChannels = ''
	begin
		insert into @TableTmp
		select distinct(filiere_id) from OpinionOrderForms where filiere_id is not null
	end



if @pStartDate <> ''
BEGIN
	set @datestart= @pStartDate
END


if @pEndDate <> ''
BEGIN
	set @dateend =@pEndDate
END


/*

set @pStructureid = 991
set @pQuestionCode = 'Q1'


*/

select @END_NUMBER = datediff(day, @datestart, @dateend)/@intervalleMinute

Declare @s table (dateevent datetime)

Insert Into @s
select
dateevent=dateadd(day, (a.NUMBER+b.NUMBER)*@intervalleMinute,@datestart)

from
      (
      Select      top 100 percent
            NUMBER = N01+N02+N03+N04+N05+N06+N07+N08+N09+N10+N11+N12
      From
            -- Cross rows from 12 tables based on powers of two
            -- Maximum number of rows from cross join is 4096, 0 to 4095
              ( select N01 = 0 union all select    1 ) n01 cross join
              ( select N02 = 0 union all select    2 ) n02 cross join
              ( select N03 = 0 union all select    4 ) n03 cross join
              ( select N04 = 0 union all select    8 ) n04 cross join
              ( select N05 = 0 union all select   16 ) n05 cross join
              ( select N06 = 0 union all select   32 ) n06 cross join
              ( select N07 = 0 union all select   64 ) n07 cross join
              ( select N08 = 0 union all select  128 ) n08 cross join
              ( select N09 = 0 union all select  256 ) n09 cross join
              ( select N10 = 0 union all select  512 ) n10 cross join
              ( select N11 = 0 union all select 1024 ) n11 cross join
              ( select N12 = 0 union all select 2048 ) n12
      where
            N01+N02+N03+N04+N05+N06+N07+N08+N09+N10+N11+N12 <
            -- Square root of total rows rounded up to next whole number
            convert(int,ceiling(sqrt(@END_NUMBER+1)))
      order by
            1
      ) a
      cross join
      (
      Select      -- Top with sort forces optimizer to create this result
            -- before starting final cross join
            top 100 percent
            NUMBER = 
            (N01+N02+N03+N04+N05+N06+N07+N08+N09+N10+N11+N12) *
            convert(int,ceiling(sqrt(@END_NUMBER+1)))
      From 
              ( select N01 = 0 union all select    1 ) n01 cross join
              ( select N02 = 0 union all select    2 ) n02 cross join
              ( select N03 = 0 union all select    4 ) n03 cross join
              ( select N04 = 0 union all select    8 ) n04 cross join
              ( select N05 = 0 union all select   16 ) n05 cross join
              ( select N06 = 0 union all select   32 ) n06 cross join
              ( select N07 = 0 union all select   64 ) n07 cross join
              ( select N08 = 0 union all select  128 ) n08 cross join
              ( select N09 = 0 union all select  256 ) n09 cross join
              ( select N10 = 0 union all select  512 ) n10 cross join
              ( select N11 = 0 union all select 1024 ) n11 cross join
              ( select N12 = 0 union all select 2048 ) n12
      where
            N01+N02+N03+N04+N05+N06+N07+N08+N09+N10+N11+N12 <
            convert(int,ceiling(sqrt(@END_NUMBER+1)))
      order by
            1
      ) b
where
      a.NUMBER+b.NUMBER < @END_NUMBER+1  and
      -- Do nothing if total rows over limit (4096*4096 = 16777216 )
      @END_NUMBER+1 <= 16777216
order by 1

--select  * from @s
DECLARE @result TABLE (the_date datetime, number int, total_score int, average_today decimal(18,2));
insert into @result
select 
convert(datetime, convert(varchar,jour) + '/' + convert(varchar,mois) + '/' + convert(varchar,annee)) as jdt,

--select jour, mois, annee,   
 --REPLICATE('0', 4 - LEN(annee)) + convert(varchar,annee) + '/' + 
--REPLICATE('0', 2 - LEN(mois)) + convert(varchar,mois) + '/' + REPLICATE('0', 2 - LEN(jour)) + convert(varchar,jour)
--as amj, 
sum(nbr) as number, sum(nbrS) as total_score, 

case when sum(nbr)>0 then  cast( sum(nbrS) as decimal(18,2)) / cast(sum(nbr) as decimal(18,2)) else 0 end as average_today


--,ROUND(case when sum(nbr)>0 then sum(nbrS)  / sum(nbr) else 0 end, 2)
from ( 
	--dates/heure
	select datepart(dd,dateevent) as jour, 
	datepart(mm,dateevent) as mois, 
	datepart(yy,dateevent) as annee,
	round(datepart(day,dateevent) / @intervalleminute,0)*@intervalleminute as mns ,0 as nbr, 0 as nbrS from @s
	union
	--donnees
	select datepart(dd,g.date_response) as jour, 
	datepart(mm,g.date_response) as mois, 
	datepart(yy,g.date_response) as annee,	
	round(datepart(DAY,g.date_response) / @intervalleminute,0)*@intervalleminute as day,
	count(distinct(g.form_id)) as nbr, sum(score) as nbrS 

	from OpinionOrderForms g
	inner join OpinionOrderFormsResponses resp on resp.form_id = g.form_id
	inner join OpinionOrderQuestions q on q.id = resp.question_id
	where structure_id=@pStructureid and
		q.code = @pQuestionCode and
		date_response>=@datestart and date_response<=dateadd(day,1,@dateend)
		and g.filiere_id in ( select filiere_id from @TableTmp )
		
	group by 
	datepart(yy,date_response),
	datepart(mm,date_response),datepart(dd,date_response)

) uni
group by jour, mois, annee 
order by annee, mois, jour



select *, 
(select sum(number) from @result r2 WHERE r2.the_date <= r.the_date) as number_progression,
(select sum(total_score) from @result r2 WHERE r2.the_date <= r.the_date) as total_score_progression
,	case when (select sum(total_score) from @result r2 WHERE r2.the_date <= r.the_date)>0 then
	(select cast( sum(total_score) as decimal(18,2)) / cast( sum(number) as decimal(18,2)) from @result r2 WHERE r2.the_date <= r.the_date) 
	else 0 end as average_progression
 from @result r 
  order by r.the_date





select FORMAT(@datestart, 'dd-MM-yyyy')  as start_date,FORMAT(@dateend, 'dd-MM-yyyy')  as end_date
