﻿/* cancelOrder.sql */

DECLARE @OkEtat int 
DECLARE @SQL VARCHAR(max)
DECLARE @Commande_ID int
DECLARE @Manif_Id int 
DECLARE @Seance_ID int
DECLARE @Dossier_id int
DECLARE @Dossier_Etat varchar(10)
DECLARE @Type_Ligne varchar(10)
DECLARE @Operateur_ID int
DECLARE @NumPaiement int
DECLARE @TransRemb int
DECLARE @TransFact int
DECLARE @TransMacptRemb int
DECLARE @TransRembAcpte int
DECLARE @TransAnnul int
DECLARE @TransRembPro int
DECLARE @TransAnnulPro int
DECLARE @IsStatuca int

Declare @FacturePayee int

DECLARE @Solde decimal(18,10)
DECLARE @Identite_ID int

-- declare @pOrderId int = 105079
-- declare @pOperatorid int = 5880001

SET @Commande_ID = @pOrderId
SET @Operateur_ID = @pOperatorid

SET @OkEtat = (SELECT count(distinct etat) FROM commande_ligne_comp WHERE commande_id = @Commande_ID AND etat not IN ('R','A'))
--print '0'
IF @OkEtat = 1 
	BEGIN
	
		IF (@Operateur_ID = 0)
		BEGIN
			/* l'operateur n'a pas été determiné en amont, todo aller le chercher ici */
			select top 1 @Operateur_ID = operateur_id FROM commande_ligne_comp WHERE commande_id = @Commande_ID AND etat not IN ('R','A')
			
		END
	

		DECLARE Curseur  cursor for
		SELECT  cl.Manifestation_ID , cl.Seance_id,cl.Dossier_id,clc.Etat,  cl.type_ligne from commande_ligne_comp clc inner join commande_ligne cl on cl.commande_ligne_id = clc.commande_ligne_id 
			WHERE cl.commande_id = @Commande_id and clc.etat in ('P','B')
		
		OPEN curseur
		
		FETCH curseur into @Manif_Id,@Seance_ID,@Dossier_id,@Dossier_Etat,@Type_Ligne
	
		--génération de la requête boucle
		WHILE @@fetch_status = 0 
			BEGIN
				print '2'
				SET @IsStatuca = (select count(*) from sys.columns where name = 'statutca')

				IF @IsStatuca =0 
					BEGIN
						set @SQL = 'INSERT INTO Recette
						SELECT [manifestation_id],[seance_id],[entree_id],'+ replace(str(@Operateur_id),' ','') + ',getdate(),''A'',[date_ouverture_caisse],[montant1],[montant2],[montant3],[montant4]
						,[montant5],[montant6],[montant7],[montant8],[montant9],[montant10],[numbillet],[categorie_id],[externe],[type_tarif_id]
						,[dossier_id],[motif],[origine],[technologie]
						from recette where dossier_id = '+ replace(str(@dossier_id),' ','') + ' and Manifestation_id = '+ replace(str(@Operateur_id),' ','') + ' and Seance_ID = '+ replace(str(@Seance_ID),' ','') + ' and type_operation = ''E'''
						print @SQL
						EXEC (@SQL)
					end 
				if @IsStatuca >0 
					BEGIN
						set @SQl = 'INSERT INTO Recette
						SELECT [manifestation_id],[seance_id],[entree_id],'+ replace(str(@Operateur_id),' ','') + ',getdate(),''A'',[date_ouverture_caisse],[montant1],[montant2],[montant3],[montant4]
						,[montant5],[montant6],[montant7],[montant8],[montant9],[montant10],[numbillet],[categorie_id],[externe],[type_tarif_id]
						,[dossier_id],[motif],[origine],[technologie],statutca
						FROM recette where dossier_id = '+ replace(str(@dossier_id),' ','') + ' and Manifestation_id = '+ replace(str(@Operateur_id),' ','') + ' and Seance_ID = '+ replace(str(@Seance_ID),' ','') + ' and type_operation = ''E'''
						print @SQL
						EXEC (@SQL)
					END

				set @SQL = 'UPDATE Dossier_'+ replace(str(@Manif_id),' ','') + ' 
				SET Dossier_v = Dossier_v+1 
				,operateur_id =  '+ replace(str(@Operateur_id),' ','') + ' 
				,Dossier_etat = ''A'', date_operation = getdate()
				WHERE seance_id = '+ replace(str(@Seance_ID),' ','') + ' 
				and dossier_id = '+ replace(str(@dossier_id),' ','') + ''

				EXEC(@SQL)

				set @SQL = 'INSERT INTO Dossiersvg_'+ replace(str(@Manif_id),' ','') + '
				SELECT 
				[dossier_id],[identite_id],[commande_id],[abon_manif_id],[seance_id]
				,[dossier_v]+1
				, '+ replace(str(@Operateur_id),' ','') + '
				,[dossier_montant]
				,[dossier_montant1],[dossier_montant2],[dossier_montant3],[dossier_montant4]
				,[dossier_montant5],[dossier_montant6],[dossier_montant7]
				,[dossier_montant8],[dossier_montant9],[dossier_montant10]
				,[dossier_c]
				,''A''
				,[dossier_icone]
				,[dossier_numero]
				,[dossier_nbplace]
				,[dossier_montantpayer]
				,[dossier_facture]
				,[dossier_client_nom]
				,[num_paiement]
				,getdate()
				,''REMB''
				,[filiere_id]
				FROM Dossiersvg_'+ replace(str(@Manif_id),' ','') + '
				WHERE seance_id = '+ replace(str(@Seance_ID),' ','') + ' 
				and dossier_id = '+ replace(str(@dossier_id),' ','') + '
				and dossier_etat = ''B'' and Type_operation = ''EDIT''
				'
				print @SQL
				EXEC(@SQL)

				set @SQL = 'UPDATE Entree_'+ replace(str(@Manif_id),' ','') + '
				SET Entree_etat = ''L'', Dossier_id = 0,dateoperation = getdate()
				WHERE seance_id = '+ replace(str(@Seance_ID),' ','') + ' 
				and dossier_id = '+ replace(str(@dossier_id),' ','') + ''
				
				EXEC(@SQL)

				SET @SQL = 'INSERT INTO Entreesvg_'+ replace(str(@Manif_id),' ','') + '
				SELECT [entree_id],[seance_id],[dossier_id], ''L'',[categorie_id]
				,[type_tarif_id]
				,'+ replace(str(@Operateur_id),' ','') + '
				,[ValeurTarifStockVersion]
				,[numero_billet]
				,[alotissement_id]
				,[reserve_id]
				,[contingent_id]
				,[montant1],[montant2],[montant3],[montant4]
				,[montant5],[montant6],[montant7],[montant8],[montant9],[montant10]
				,getdate(), dossier_v+1
				FROM Entreesvg_'+ replace(str(@Manif_id),' ','') + '
				WHERE seance_id = '+ replace(str(@Seance_ID),' ','') + ' 
				and dossier_id = '+ replace(str(@dossier_id),' ','') + '
				and entree_etat = ''B''				
				'
				exec(@SQL)
				
				declare @SommeFacturePayee decimal(18,10)
				declare @SommeFactureSolde decimal(18,10)

				set @FacturePayee = (select count(*) from compte_client where commande_id = @commande_id and cc_intitule_operation = 'CREDITFAC')

				if @FacturePayee > 0  
					begin
					set @SommeFacturePayee = (select sum(montant1) from compte_client where commande_id = @Commande_id and cc_intitule_operation = 'CREDITFAC')
					set @SommeFactureSolde = (select sum(montant1) from compte_client where commande_id = @Commande_id and cc_intitule_operation = 'DEBITFAC')
					end			
					
				UPDATE Commande_ligne_comp set etat = 'A' ,dossier_icone =1024
				WHERE seance_id = @Seance_ID and manifestation_id = @Manif_Id and dossier_id = @Dossier_id 

				INSERT INTO histodossier 
				SELECT [commande_id],[dossier_v]+1,[type],'A',getdate(),@Operateur_id,[manif_id],[seance_id],[dossier_id]
				FROM histodossier 
				WHERE seance_id = @Seance_ID and manif_id = @Manif_Id and dossier_id = @dossier_id

				SET @NumPaiement = (SELECT TOP 1 compteur+1 from cpt_paiement) 
				UPDATE cpt_paiement SET Compteur=@NumPaiement 

				SET @TransRemb = (SELECT TOP 1 compteur+1 from cpt_transaction) 
				UPDATE cpt_transaction SET Compteur=@TransRemb
				INSERT INTO compte_client 
				SELECT Identite_id,@TransRemb 
				,'REMBDOS',cc_credit,0,0,@operateur_id,getdate(),0,'','','',@Manif_id,@NumPaiement 
				,'REMBDOS',0,@seance_id,@dossier_id,montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,@Commande_id,0,0
				FROM compte_client where  dossier_id = @Dossier_id and Manif_id = @Manif_Id and Seance_ID = @Seance_ID and cc_intitule_operation = 'CREDITDOS'

				SET @TransRembPro = (SELECT TOP 1 compteur+1 from cpt_transaction) 
				UPDATE cpt_transaction SET Compteur=@TransRembPro
				INSERT INTO compte_client 
				SELECT  Identite_id,@TransRembPro 
				,'REMBPRO',cc_credit,0,0,@operateur_id,getdate(),0,'','','',@Manif_id,@NumPaiement 
				,'REMBPRO',0,@seance_id,@dossier_id,montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,@Commande_id,0,0
				FROM compte_client where  dossier_id = @Dossier_id and Manif_id = @Manif_Id and Seance_ID = @Seance_ID and cc_intitule_operation = 'CREDITPRO'

				if @SommeFacturePayee >0 
					begin
						
						set @TransMacptRemb = (SELECT TOP 1 compteur+1 from cpt_transaction) 
						UPDATE cpt_transaction SET Compteur=@TransMacptRemb
						INSERT INTO compte_client 
						SELECT Identite_id,@TransMacptRemb 
						,'MACPTREMB',0,cc_credit,0,@operateur_id,getdate(),0,'','','',@Manif_id,@NumPaiement 
						,'MACPTREMB',0,@seance_id,@dossier_id,montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,@Commande_id,0,0
						from compte_client where  dossier_id = @Dossier_id and Manif_id = @Manif_Id and Seance_ID = @Seance_ID and cc_intitule_operation = 'CREDITFAC'

						set @TransRembAcpte = (SELECT TOP 1 compteur+1 from cpt_transaction) 
						UPDATE cpt_transaction SET Compteur=@TransRembAcpte
						INSERT INTO compte_client 
						SELECT Identite_id,@TransRembAcpte 
						,'REMBACPTE',cc_credit,0,0,@operateur_id,getdate(),0,'','','',@Manif_id,@NumPaiement 
						,'REMBACPTE',0,@seance_id,@dossier_id,montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,@Commande_id,0,0
						from compte_client where  dossier_id = @Dossier_id and Manif_id = @Manif_Id and Seance_ID = @Seance_ID and cc_intitule_operation = 'CREDITFAC'

						INSERT INTO compte_transaction values (@TransRemb,@TransMacptRemb,'',@operateur_id,getdate(),'REMB',0,0,0,'',0,0,0,0,0,0,0,0) 
						INSERT INTO compte_transaction values (@TransRembPro,@TransMacptRemb,'',@operateur_id,getdate(),'REMB',0,0,0,'',0,0,0,0,0,0,0,0)


						set @TransFact = (SELECT TOP 1 compteur+1 from cpt_transaction) 
						UPDATE cpt_transaction SET Compteur=@TransFact
						INSERT INTO compte_client 
						SELECT Identite_id,@TransFact 
						,'DECHIRFAC',0,@SommeFactureSolde,0,@operateur_id,getdate(),0,'','','',@Manif_id,@NumPaiement 
						,'DECHIRFAC',0,@seance_id,@dossier_id,@SommeFactureSolde,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,@Commande_id,0,0
						from compte_client where  dossier_id = @Dossier_id and Manif_id = @Manif_Id and Seance_ID = @Seance_ID and cc_intitule_operation = 'DEBITFAC'

						INSERT INTO compte_transaction values (@TransRemb,@TransFact,'',@operateur_id,getdate(),'REMB',0,0,0,'',0,0,0,0,0,0,0,0) 
						INSERT INTO compte_transaction values (@TransRembPro,@TransFact,'',@operateur_id,getdate(),'REMB',0,0,0,'',0,0,0,0,0,0,0,0)

					end
				if @SommeFacturePayee >0 
					begin
						set @TransFact = (SELECT TOP 1 compteur+1 from cpt_transaction) 
						UPDATE cpt_transaction SET Compteur=@TransFact
						INSERT INTO compte_client 
						SELECT Identite_id,@TransFact 
						,'DECHIRFAC',0,cc_debit,0,@operateur_id,getdate(),0,'','','',@Manif_id,@NumPaiement 
						,'DECHIRFAC',0,@seance_id,@dossier_id,montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,@Commande_id,0,0
						from compte_client where  dossier_id = @Dossier_id and Manif_id = @Manif_Id and Seance_ID = @Seance_ID and cc_intitule_operation = 'DEBITFAC'
						
						INSERT INTO compte_transaction values (@TransRemb,@TransFact,'',@operateur_id,getdate(),'REMB',0,0,0,'',0,0,0,0,0,0,0,0) 
						INSERT INTO compte_transaction values (@TransRembPro,@TransFact,'',@operateur_id,getdate(),'REMB',0,0,0,'',0,0,0,0,0,0,0,0) 
					
					end

				set @TransAnnul = (SELECT TOP 1 compteur+1 from cpt_transaction) 
				UPDATE cpt_transaction SET Compteur=@TransAnnul
				INSERT INTO compte_client 
				SELECT Identite_id,@TransAnnul 
					,'ANNULDOS',0,cc_credit,0,@operateur_id,getdate(),0,'','','',@Manif_id,@NumPaiement 
					,'ANNULDOS',0,@seance_id,@dossier_id,montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,@Commande_id,0,0
				from compte_client where  dossier_id = @Dossier_id and Manif_id = @Manif_Id and Seance_ID = @Seance_ID and cc_intitule_operation = 'CREDITDOS'

				set @TransAnnulPro = (SELECT TOP 1 compteur+1 from cpt_transaction) 
				UPDATE cpt_transaction SET Compteur=@TransAnnulPro
				INSERT INTO compte_client 
				SELECT Identite_id,@TransAnnulPro 
					,'ANNULPRO',0,cc_credit,0,@operateur_id,getdate(),0,'','','',@Manif_id,@NumPaiement 
					,'ANNULPRO',0,@seance_id,@dossier_id,montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,@Commande_id,0,0
				FROM compte_client WHERE  dossier_id = @Dossier_id and Manif_id = @Manif_Id and Seance_ID = @Seance_ID and cc_intitule_operation = 'CREDITPRO'

				SET @Solde = (select sum(cc_debit) from compte_client where dossier_id = @Dossier_id and Manif_id = @Manif_Id and Seance_ID = @Seance_ID and cc_intitule_operation = 'DEBITFAC')
				SET @Identite_ID = (select identite_id from compte_client where dossier_id = @Dossier_id and Manif_id = @Manif_Id and Seance_ID = @Seance_ID and cc_intitule_operation = 'DEBITFAC')

				UPDATE identite set montant_credit =  montant_credit - @Solde WHERE identite_id = @Identite_ID				 
			    

				FETCH curseur into @Manif_Id,@Seance_ID,@Dossier_id,@Dossier_Etat,@Type_Ligne

			END
		CLOSE curseur
		DEALLOCATE curseur  
	END

select Commande_id , dossier_id , etat from commande_ligne_comp where commande_id = @Commande_id