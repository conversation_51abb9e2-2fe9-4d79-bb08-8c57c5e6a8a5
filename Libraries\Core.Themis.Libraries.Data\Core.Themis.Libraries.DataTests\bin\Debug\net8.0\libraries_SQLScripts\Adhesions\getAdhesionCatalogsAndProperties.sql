
/*

declare @pPropCodeWanted varchar(50) 
set @pPropCodeWanted = ''
*/



/*
select * from Adhesion_Catalog ac 
	inner join Adhesion_Catalog_Propriete acp on ac.Adhesion_Catalog_ID = acp.Adhesion_Catalog_ID
	inner join adhesion_catalog_offresliees aco on aco.adhesion_catalog_id = acp.Adhesion_Catalog_ID
	where ac.Adhesion_Catalog_ID in({pListAdhesionsCatalogsIds})  --and Propriete_Code in (@pPropCodeWanted);

*/


select f.filiere_id, f.filiere_nom, tt.type_tarif_id, tt.type_tarif_nom, * from Adhesion_Catalog ac 
	inner join Adhesion_Catalog_Propriete acp on ac.Adhesion_Catalog_ID = acp.Adhesion_Catalog_ID
	inner join adhesion_catalog_offresliees aco on aco.adhesion_catalog_id = acp.Adhesion_Catalog_ID
	
		left outer JOIN type_tarif AS tt 
   ON tt.type_tarif_id = acp.propriete_valeur_int1

       AND (acp.propriete_code = 'TARIF_MAITRE' 
            OR acp.propriete_code = 'TARIF_ELEVE')


	left outer join filiere f 
	on f.filiere_id = acp.Propriete_Valeur_Int1 and (acp.Propriete_Code='FILIERE_ADH'  OR acp.propriete_code = 'FILIERE_HORS_ADH')

	where ac.Adhesion_Catalog_ID in({pListAdhesionsCatalogsIds})  --and Propriete_Code in (@pPropCodeWanted);

