using BlazorBootstrap;
using Microsoft.AspNetCore.Components;

namespace ThemisSupportTools.Web.Components.Pages.Modules.Partners
{
    public partial class Partners
    {

        [Parameter]
        public string? SearchTerm { get; set; }

        protected Core.Themis.Libraries.Razor.Common.Components.Dialog DeleteConfirmation { get; set; } = new();

        private List<PartnerDTO> partenaires = new();
        private PartnerDTO partnerDTOSelected = new();
        public PartnerDTO PartnerDTO { get; set; } = new PartnerDTO();
        public List<PartnerRoleDTO> PartnerRolesDTO { get; set; } = new();



        private bool isLoading = true;

        const string MODULE_NAME = "partners";
        List<ToastMessage> toastMessages = new();
        ToastsPlacement toastsPlacement = ToastsPlacement.TopRight;
        private int? currentlySelectedPartnerId;
        private BlazorBootstrap.Modal modal = default!;
        private bool isEditMode = false;
        private ConfirmDialog dialog = default!;


        protected override async Task OnInitializedAsync()
        {
            if (TstAccessService.IsGranted(MODULE_NAME))
            {
                LoadPartners();
                isLoading = false;
            }
        }


        private async Task OnCancelForm()
        {
            LoadPartners();
            await modal.HideAsync();
        }


        private async Task OnPartnerSubmitted(PartnerDTO partnerDTO)
        {

            if (isEditMode)
            {
                await PartenairesManager.UpdatePartnerWithDependanciesAsync(partnerDTO);
                await modal.HideAsync();
            }
            else
            {

                try
                {
                    int partnerId = PartenairesManager.InsertPartner(partnerDTO);
                    PartnerDTO.PartnerId = partnerId;
                    currentlySelectedPartnerId = PartnerDTO.PartnerId;
                    await modal.HideAsync();
                }
                catch (Exception ex)
                {
                    ShowErrorAlert(Localizer["error_exist_partener_title"], Localizer["error_partner_exists", PartnerDTO.PartnerName]);

                }

            }

            LoadPartners();
        }


        private void ShowErrorAlert(string title, string message)
        {
            ToastMessage toastMessage = new()
            {
                Type = ToastType.Danger,
                AutoHide = true,
                Title = title,
                Message = message
            };

            toastMessages.Add(toastMessage);
        }


        private void DeletePartner(PartnerDTO partner)
        {
            partnerDTOSelected = partner;
            DeleteConfirmation.Show();
        }


        protected void ConfirmDelete_Click(bool deleteConfirmed)
        {
            if (deleteConfirmed)
            {
                partnerDTOSelected.DateSupp = DateTime.Now;
                PartenairesManager.UpdatePartnerAsync(partnerDTOSelected);
                partenaires = PartenairesManager.GetAcivePartnersOrderByName();
                ShowSuccessAlert();

            }

        }

        private async Task EditPartner(PartnerDTO Partner)
        {
            isEditMode = true;
            PartnerDTO = Partner;
            currentlySelectedPartnerId = Partner.PartnerId;

            await modal.ShowAsync();
        }

        private async Task ShowCreateForm()
        {
            isEditMode = false;
            currentlySelectedPartnerId = null;
            PartnerDTO = new();
            StateHasChanged();
            await modal.ShowAsync();
        }

        private void ShowSuccessAlert()
        {
            ToastMessage toastMessage = new()
            {
                Type = ToastType.Success,
                AutoHide = true,
                Title = Localizer["toast_message_title_deletion_success"],
                Message = Localizer["toast_message_deletion_successful"]
            };

            toastMessages.Add(toastMessage);
        }


        private void LoadPartners()
        {
            partenaires = PartenairesManager.GetAllPartners().OrderBy(v => v.PartnerName).ToList();
        }

        private async Task OnSearchAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                await Task.Run(() => LoadPartners());
            }
            else
            {
                await Task.Run(() =>
                {
                    partenaires = PartenairesManager.GetAllPartners()
                                                     .Where(p => p.PartnerName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
                                                     .OrderBy(v => v.PartnerName)
                                                     .ToList();
                });
            }

            StateHasChanged();
        }


    }
}
