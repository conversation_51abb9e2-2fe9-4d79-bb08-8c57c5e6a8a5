is_global = true
build_property.TargetFramework = net8.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows,browser
build_property.RootNamespace = Core.Themis.Libraries.Razor
build_property.RootNamespace = Core.Themis.Libraries.Razor
build_property.ProjectDir = D:\WORK\TST_LD\ThemisSupportTools_LD\Libraries\Core.Themis.Libraries.Razor\Core.Themis.Libraries.Razor\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 8.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = true
build_property.MSBuildProjectDirectory = D:\WORK\TST_LD\ThemisSupportTools_LD\Libraries\Core.Themis.Libraries.Razor\Core.Themis.Libraries.Razor
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 8.0
build_property.EnableCodeStyleSeverity = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/AdminSphereAreaApp.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcQWRtaW5TcGhlcmVBcmVhQXBwLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/Components/Catalog/AreaSettingsForm.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcQ29tcG9uZW50c1xDYXRhbG9nXEFyZWFTZXR0aW5nc0Zvcm0ucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/Components/Catalog/GeneralSettingsForm.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcQ29tcG9uZW50c1xDYXRhbG9nXEdlbmVyYWxTZXR0aW5nc0Zvcm0ucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/Components/Catalog/MainMenuItemSettingsForm.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcQ29tcG9uZW50c1xDYXRhbG9nXE1haW5NZW51SXRlbVNldHRpbmdzRm9ybS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/Components/Event/OneGpEventLockTranslationForm.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcQ29tcG9uZW50c1xFdmVudFxPbmVHcEV2ZW50TG9ja1RyYW5zbGF0aW9uRm9ybS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/Components/Event/OneGpEventWaitingTranslationForm.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcQ29tcG9uZW50c1xFdmVudFxPbmVHcEV2ZW50V2FpdGluZ1RyYW5zbGF0aW9uRm9ybS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/Components/Event/OneGpSessionLockTranslationForm.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcQ29tcG9uZW50c1xFdmVudFxPbmVHcFNlc3Npb25Mb2NrVHJhbnNsYXRpb25Gb3JtLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/Components/PassCulture/AddressForm.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcQ29tcG9uZW50c1xQYXNzQ3VsdHVyZVxBZGRyZXNzRm9ybS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/Components/PassCulture/ContactFieldSet.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcQ29tcG9uZW50c1xQYXNzQ3VsdHVyZVxDb250YWN0RmllbGRTZXQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/Components/PassCulture/EducationalFieldSet.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcQ29tcG9uZW50c1xQYXNzQ3VsdHVyZVxFZHVjYXRpb25hbEZpZWxkU2V0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/Components/PassCulture/Error/SettingsErrorsInfos.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcQ29tcG9uZW50c1xQYXNzQ3VsdHVyZVxFcnJvclxTZXR0aW5nc0Vycm9yc0luZm9zLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/Components/PassCulture/IndividualOffer/CategoryEventForm.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcQ29tcG9uZW50c1xQYXNzQ3VsdHVyZVxJbmRpdmlkdWFsT2ZmZXJcQ2F0ZWdvcnlFdmVudEZvcm0ucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/Components/PassCulture/IndividualOffer/ChoiseManifForm.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcQ29tcG9uZW50c1xQYXNzQ3VsdHVyZVxJbmRpdmlkdWFsT2ZmZXJcQ2hvaXNlTWFuaWZGb3JtLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/Components/PassCulture/IndividualOffer/GlobalInfoSuppForm.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcQ29tcG9uZW50c1xQYXNzQ3VsdHVyZVxJbmRpdmlkdWFsT2ZmZXJcR2xvYmFsSW5mb1N1cHBGb3JtLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/Components/PassCulture/OfferFieldSet.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcQ29tcG9uZW50c1xQYXNzQ3VsdHVyZVxPZmZlckZpZWxkU2V0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/Components/PassCulture/PlaceFieldSet.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcQ29tcG9uZW50c1xQYXNzQ3VsdHVyZVxQbGFjZUZpZWxkU2V0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/Pages/Catalog/CustomSettingsCatalog.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcUGFnZXNcQ2F0YWxvZ1xDdXN0b21TZXR0aW5nc0NhdGFsb2cucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/Pages/Catalog/ListOfferCatalogs.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcUGFnZXNcQ2F0YWxvZ1xMaXN0T2ZmZXJDYXRhbG9ncy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/Pages/Events/LockListForEvents.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcUGFnZXNcRXZlbnRzXExvY2tMaXN0Rm9yRXZlbnRzLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/Pages/Events/WaitingListForEvents.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcUGFnZXNcRXZlbnRzXFdhaXRpbmdMaXN0Rm9yRXZlbnRzLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/Pages/PassCulture/CollectiveOfferForm.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcUGFnZXNcUGFzc0N1bHR1cmVcQ29sbGVjdGl2ZU9mZmVyRm9ybS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/Pages/PassCulture/IndividualOfferForm.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcUGFnZXNcUGFzc0N1bHR1cmVcSW5kaXZpZHVhbE9mZmVyRm9ybS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/Pages/PassCulture/PassCultureSettings.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcUGFnZXNcUGFzc0N1bHR1cmVcUGFzc0N1bHR1cmVTZXR0aW5ncy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/Pages/PrinterProof/PrinterProofForm.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcUGFnZXNcUHJpbnRlclByb29mXFByaW50ZXJQcm9vZkZvcm0ucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/Pages/PrinterProof/PrinterProofSettings.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcUGFnZXNcUHJpbnRlclByb29mXFByaW50ZXJQcm9vZlNldHRpbmdzLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/Shared/MainLayout.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcU2hhcmVkXE1haW5MYXlvdXQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/Catalog/CatalogApp.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ2F0YWxvZ1xDYXRhbG9nQXBwLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/Catalog/Components/Area.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ2F0YWxvZ1xDb21wb25lbnRzXEFyZWEucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/Catalog/Components/Filters.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ2F0YWxvZ1xDb21wb25lbnRzXEZpbHRlcnMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/Catalog/Components/OneCatalogCard.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ2F0YWxvZ1xDb21wb25lbnRzXE9uZUNhdGFsb2dDYXJkLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/Catalog/Components/Products/DynamicProductForm.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ2F0YWxvZ1xDb21wb25lbnRzXFByb2R1Y3RzXER5bmFtaWNQcm9kdWN0Rm9ybS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/Catalog/Components/Products/OneProduct.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ2F0YWxvZ1xDb21wb25lbnRzXFByb2R1Y3RzXE9uZVByb2R1Y3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/Catalog/Components/Products/OneProductAdhesionCard.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ2F0YWxvZ1xDb21wb25lbnRzXFByb2R1Y3RzXE9uZVByb2R1Y3RBZGhlc2lvbkNhcmQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/Catalog/Components/Products/ProduitMontantFixeForm.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ2F0YWxvZ1xDb21wb25lbnRzXFByb2R1Y3RzXFByb2R1aXRNb250YW50Rml4ZUZvcm0ucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/Catalog/Components/Products/ProduitMontantVariableForm.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ2F0YWxvZ1xDb21wb25lbnRzXFByb2R1Y3RzXFByb2R1aXRNb250YW50VmFyaWFibGVGb3JtLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/Catalog/Pages/Catalog.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ2F0YWxvZ1xQYWdlc1xDYXRhbG9nLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/Catalog/Pages/ProductDetail.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ2F0YWxvZ1xQYWdlc1xQcm9kdWN0RGV0YWlsLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/Catalog/Shared/MainLayout.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ2F0YWxvZ1xTaGFyZWRcTWFpbkxheW91dC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/CustomerArea/Components/Consumer/CreateConsumerForm.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ3VzdG9tZXJBcmVhXENvbXBvbmVudHNcQ29uc3VtZXJcQ3JlYXRlQ29uc3VtZXJGb3JtLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/CustomerArea/Components/Consumer/DynamicFormField.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ3VzdG9tZXJBcmVhXENvbXBvbmVudHNcQ29uc3VtZXJcRHluYW1pY0Zvcm1GaWVsZC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/CustomerArea/Components/Consumer/SeachConsumerForm.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ3VzdG9tZXJBcmVhXENvbXBvbmVudHNcQ29uc3VtZXJcU2VhY2hDb25zdW1lckZvcm0ucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/CustomerArea/Components/Login/LoginAmz.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ3VzdG9tZXJBcmVhXENvbXBvbmVudHNcTG9naW5cTG9naW5BbXoucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/CustomerArea/Components/Login/LoginContainer.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ3VzdG9tZXJBcmVhXENvbXBvbmVudHNcTG9naW5cTG9naW5Db250YWluZXIucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/CustomerArea/Components/Login/LoginCreate.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ3VzdG9tZXJBcmVhXENvbXBvbmVudHNcTG9naW5cTG9naW5DcmVhdGUucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/CustomerArea/Components/Login/LoginForm.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ3VzdG9tZXJBcmVhXENvbXBvbmVudHNcTG9naW5cTG9naW5Gb3JtLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/CustomerArea/Components/Order/HeaderOrderDetails.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ3VzdG9tZXJBcmVhXENvbXBvbmVudHNcT3JkZXJcSGVhZGVyT3JkZXJEZXRhaWxzLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/CustomerArea/Components/Order/OneOrderCard.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ3VzdG9tZXJBcmVhXENvbXBvbmVudHNcT3JkZXJcT25lT3JkZXJDYXJkLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/CustomerArea/Components/Order/OrderCardList.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ3VzdG9tZXJBcmVhXENvbXBvbmVudHNcT3JkZXJcT3JkZXJDYXJkTGlzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/CustomerArea/Components/Ticket/Tickets_OneSession.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ3VzdG9tZXJBcmVhXENvbXBvbmVudHNcVGlja2V0XFRpY2tldHNfT25lU2Vzc2lvbi5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/CustomerArea/Components/Ticket/Tickets_SessionsList.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ3VzdG9tZXJBcmVhXENvbXBvbmVudHNcVGlja2V0XFRpY2tldHNfU2Vzc2lvbnNMaXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/CustomerArea/Components/Ticket/Ticket_JyVaisPlus.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ3VzdG9tZXJBcmVhXENvbXBvbmVudHNcVGlja2V0XFRpY2tldF9KeVZhaXNQbHVzLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/CustomerArea/Components/Ticket/Ticket_SetName.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ3VzdG9tZXJBcmVhXENvbXBvbmVudHNcVGlja2V0XFRpY2tldF9TZXROYW1lLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/CustomerArea/CustomerAreaApp.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ3VzdG9tZXJBcmVhXEN1c3RvbWVyQXJlYUFwcC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/CustomerArea/Pages/Login/Login.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ3VzdG9tZXJBcmVhXFBhZ2VzXExvZ2luXExvZ2luLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/CustomerArea/Pages/Login/PassWordReset.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ3VzdG9tZXJBcmVhXFBhZ2VzXExvZ2luXFBhc3NXb3JkUmVzZXQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/CustomerArea/Pages/Order/OrderDetails.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ3VzdG9tZXJBcmVhXFBhZ2VzXE9yZGVyXE9yZGVyRGV0YWlscy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/CustomerArea/Pages/Order/Orders.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ3VzdG9tZXJBcmVhXFBhZ2VzXE9yZGVyXE9yZGVycy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/CustomerArea/Pages/Tickets/Tickets.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ3VzdG9tZXJBcmVhXFBhZ2VzXFRpY2tldHNcVGlja2V0cy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/CustomerArea/Shared/MainLayout.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQ3VzdG9tZXJBcmVhXFNoYXJlZFxNYWluTGF5b3V0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/Offer/OfferApp.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcT2ZmZXJcT2ZmZXJBcHAucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/Offer/Pages/Basket/MiniBasket.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcT2ZmZXJcUGFnZXNcQmFza2V0XE1pbmlCYXNrZXQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/Offer/Shared/MainLayout.razor]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcT2ZmZXJcU2hhcmVkXE1haW5MYXlvdXQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Common/Components/Collapse/Accordion.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tbW9uXENvbXBvbmVudHNcQ29sbGFwc2VcQWNjb3JkaW9uLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Common/Components/Collapse/AccordionItem.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tbW9uXENvbXBvbmVudHNcQ29sbGFwc2VcQWNjb3JkaW9uSXRlbS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Common/Components/Collapse/CollapseContent.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tbW9uXENvbXBvbmVudHNcQ29sbGFwc2VcQ29sbGFwc2VDb250ZW50LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Common/Components/Collapse/CollapseHeader.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tbW9uXENvbXBvbmVudHNcQ29sbGFwc2VcQ29sbGFwc2VIZWFkZXIucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Common/Components/Dialog.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tbW9uXENvbXBvbmVudHNcRGlhbG9nLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Common/Components/InputGroup/LanguagesInputs.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tbW9uXENvbXBvbmVudHNcSW5wdXRHcm91cFxMYW5ndWFnZXNJbnB1dHMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Common/Components/Inputs/InputCustom.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tbW9uXENvbXBvbmVudHNcSW5wdXRzXElucHV0Q3VzdG9tLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Common/Components/Inputs/InputImageFileCustom.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tbW9uXENvbXBvbmVudHNcSW5wdXRzXElucHV0SW1hZ2VGaWxlQ3VzdG9tLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Common/Components/Inputs/InputTextAreaCustom.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tbW9uXENvbXBvbmVudHNcSW5wdXRzXElucHV0VGV4dEFyZWFDdXN0b20ucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Common/Components/Inputs/IntlTelInput.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tbW9uXENvbXBvbmVudHNcSW5wdXRzXEludGxUZWxJbnB1dC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Common/Components/Loading.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tbW9uXENvbXBvbmVudHNcTG9hZGluZy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Common/Components/ModalDialog/Dialog.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tbW9uXENvbXBvbmVudHNcTW9kYWxEaWFsb2dcRGlhbG9nLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Common/Components/ModalDialog/ModalDialog.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tbW9uXENvbXBvbmVudHNcTW9kYWxEaWFsb2dcTW9kYWxEaWFsb2cucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Common/Components/ModalDialog/PopUp.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tbW9uXENvbXBvbmVudHNcTW9kYWxEaWFsb2dcUG9wVXAucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Common/Components/Pagination.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tbW9uXENvbXBvbmVudHNcUGFnaW5hdGlvbi5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Common/Components/SearchBox.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tbW9uXENvbXBvbmVudHNcU2VhcmNoQm94LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Common/Components/Select/EventSelect.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tbW9uXENvbXBvbmVudHNcU2VsZWN0XEV2ZW50U2VsZWN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Common/Components/Select/MultipleSelect.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tbW9uXENvbXBvbmVudHNcU2VsZWN0XE11bHRpcGxlU2VsZWN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Common/Components/Select/NewSelect2.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tbW9uXENvbXBvbmVudHNcU2VsZWN0XE5ld1NlbGVjdDIucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Common/Components/Select/Select2.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tbW9uXENvbXBvbmVudHNcU2VsZWN0XFNlbGVjdDIucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Common/Components/TypesComponents/DynamicLabel.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tbW9uXENvbXBvbmVudHNcVHlwZXNDb21wb25lbnRzXER5bmFtaWNMYWJlbC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Common/Components/TypesComponents/DynamicListSelect.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tbW9uXENvbXBvbmVudHNcVHlwZXNDb21wb25lbnRzXER5bmFtaWNMaXN0U2VsZWN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Common/Components/TypesComponents/DynamicSelect.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tbW9uXENvbXBvbmVudHNcVHlwZXNDb21wb25lbnRzXER5bmFtaWNTZWxlY3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/_Imports.razor]
build_metadata.AdditionalFiles.TargetPath = X0ltcG9ydHMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Common/Components/Sortable/SortableList.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tbW9uXENvbXBvbmVudHNcU29ydGFibGVcU29ydGFibGVMaXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = b-3l2pgwgbg6

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Common/Components/Toggle.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tbW9uXENvbXBvbmVudHNcVG9nZ2xlLnJhem9y
build_metadata.AdditionalFiles.CssScope = b-baenymr98i

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/Views/Catalog/Modal/AddMenuItemModal.cshtml]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcVmlld3NcQ2F0YWxvZ1xNb2RhbFxBZGRNZW51SXRlbU1vZGFsLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/Views/Catalog/Modal/CatalogTemplateChoiceModal.cshtml]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcVmlld3NcQ2F0YWxvZ1xNb2RhbFxDYXRhbG9nVGVtcGxhdGVDaG9pY2VNb2RhbC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Areas/AdminSphere/Views/Catalog/Modal/MenuItemCheckboxFormArea.cshtml]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQWRtaW5TcGhlcmVcVmlld3NcQ2F0YWxvZ1xNb2RhbFxNZW51SXRlbUNoZWNrYm94Rm9ybUFyZWEuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Common/Views/ConfirmationModal.cshtml]
build_metadata.AdditionalFiles.TargetPath = Q29tbW9uXFZpZXdzXENvbmZpcm1hdGlvbk1vZGFsLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Common/Views/InputFileCustomizeModal.cshtml]
build_metadata.AdditionalFiles.TargetPath = Q29tbW9uXFZpZXdzXElucHV0RmlsZUN1c3RvbWl6ZU1vZGFsLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/WORK/TST_LD/ThemisSupportTools_LD/Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor/Common/Views/Toast.cshtml]
build_metadata.AdditionalFiles.TargetPath = Q29tbW9uXFZpZXdzXFRvYXN0LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 
