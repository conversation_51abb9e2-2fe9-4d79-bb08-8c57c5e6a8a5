/*
DECLARE @pSortBy VARCHAR(MAX) = 'pricedesc' --nameasc, namedesc, priceasc, pricedesc
DECLARE @pSearchTerm VARCHAR(MAX) = ''
DECLARE @pLangCode VARCHAR(MAX) = 'fr'
DECLARE @pFormulasId VARCHAR(MAX) = '4,3'
*/

DECLARE @language_id INT = ISNULL((SELECT langue_id FROM langue WHERE langue_code = @pLangCode), 1)

DECLARE @TempTableAbos TABLE (
	form_abon_id INT,
	form_abon_nom VARCHAR(MAX),
	form_abon_manifmin INT,
	form_abon_manifmax INT)

IF (@pFormulasId <> '') 
BEGIN
	INSERT INTO @TempTableAbos
		SELECT DISTINCT fa.form_abon_id, ISNULL(tfa.form_abon_nom, fa.form_abon_nom), fa.form_abon_manifmin, fa.form_abon_manifmax
		FROM formule_abonnement fa
		INNER JOIN gestion_place_DispoAbosZES gpda ON gpda.formule_id = fa.form_abon_id
		LEFT OUTER JOIN traduction_formule_abonnement tfa ON tfa.form_abon_id = fa.form_abon_id  AND tfa.langue_id = @language_id
		WHERE fa.form_abon_id in(SELECT name FROM splitstring(@pFormulasId, ','))
		AND forcer = 1  
		AND 1 = ALL (SELECT gpAll.isvalide 
					 FROM gestion_place gpAll 
					 WHERE gpAll.formule_id = fa.form_abon_id)
END

IF @pSearchTerm <> ''
BEGIN
	DELETE a FROM @TempTableAbos a
	WHERE a.form_abon_nom NOT LIKE '%'+ @pSearchTerm + '%'
END

IF @pSortBy = 'nameasc'
BEGIN
	SELECT * FROM @TempTableAbos a
	ORDER BY CONCAT('N' , form_abon_nom) ASC 
END

IF @pSortBy = 'namedesc'
BEGIN
	SELECT * FROM @TempTableAbos a
	ORDER BY CONCAT('N' , form_abon_nom) DESC 
END
	
IF @pSortBy = 'priceasc'
BEGIN
	SELECT DISTINCT a.*, MIN(lpa.amount) AS price_amount 
	FROM @TempTableAbos a
	LEFT OUTER JOIN LowerPriceAbo lpa ON a.form_abon_id = lpa.abo_id
	GROUP BY a.form_abon_id, a.form_abon_nom, a.form_abon_manifmax, a.form_abon_manifmin
	ORDER BY price_amount ASC
END

IF @pSortBy = 'pricedesc'
BEGIN
	SELECT DISTINCT a.*, MAX(lpa.amount) AS price_amount 
	FROM @TempTableAbos a
	LEFT OUTER JOIN LowerPriceAbo lpa ON a.form_abon_id = lpa.abo_id
	GROUP BY a.form_abon_id, a.form_abon_nom, a.form_abon_manifmax, a.form_abon_manifmin
	ORDER BY price_amount DESC
END